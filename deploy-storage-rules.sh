#!/bin/bash

# Deploy Firebase Storage Rules and Test Profile Upload Fix
# This script deploys the new storage.rules and restarts the development server

set -e

echo "🔥 Deploying Firebase Storage Rules and Testing Profile Upload Fix..."

# Check if Firebase CLI is installed
if ! command -v firebase &> /dev/null; then
    echo "❌ Firebase CLI not found. Please install it first:"
    echo "   npm install -g firebase-tools"
    exit 1
fi

# Check if we're logged in to Firebase
if ! firebase projects:list &> /dev/null; then
    echo "❌ Not logged in to Firebase. Please run:"
    echo "   firebase login"
    exit 1
fi

# Deploy Firebase Storage Rules
echo "📋 Deploying Firebase Storage security rules..."
if firebase deploy --only storage; then
    echo "✅ Firebase Storage rules deployed successfully"
else
    echo "❌ Failed to deploy Firebase Storage rules"
    echo "💡 Make sure you're in the correct Firebase project directory"
    echo "💡 Run 'firebase use --add' to select the correct project"
    exit 1
fi

# Kill any existing development processes
echo "🔄 Stopping existing development servers..."
pkill -f "python.*app.py" || true
pkill -f "npm.*start" || true
pkill -f "node.*server" || true

# Wait a moment for processes to stop
sleep 2

# Start backend server
echo "🚀 Starting backend server..."
cd backend
python3.10 app.py &
BACKEND_PID=$!
cd ..

# Wait for backend to start
echo "⏳ Waiting for backend to start..."
sleep 5

# Check if backend is running
if ! curl -s http://localhost:5000/health > /dev/null; then
    echo "❌ Backend failed to start"
    kill $BACKEND_PID 2>/dev/null || true
    exit 1
fi

echo "✅ Backend server started successfully"

# Start frontend development server
echo "🚀 Starting frontend development server..."
npm start &
FRONTEND_PID=$!

# Wait for frontend to start
echo "⏳ Waiting for frontend to start..."
sleep 10

# Check if frontend is running
if ! curl -s http://localhost:3000 > /dev/null; then
    echo "❌ Frontend failed to start"
    kill $BACKEND_PID $FRONTEND_PID 2>/dev/null || true
    exit 1
fi

echo "✅ Frontend server started successfully"

echo ""
echo "🎉 Profile Upload Fix Deployment Complete!"
echo ""
echo "📋 What was fixed:"
echo "   ✅ Created Firebase Storage security rules (storage.rules)"
echo "   ✅ Improved error handling in image upload utilities"
echo "   ✅ Enhanced loading state management"
echo "   ✅ Fixed profile page glassmorphic styling"
echo "   ✅ Repositioned Profile menu item above Sign Out"
echo "   ✅ Improved color contrast for better readability"
echo ""
echo "🌐 Application URLs:"
echo "   Frontend: http://localhost:3000"
echo "   Backend:  http://localhost:5000"
echo ""
echo "🧪 Testing Instructions:"
echo "   1. Open http://localhost:3000 in your browser"
echo "   2. Sign in with your Google account"
echo "   3. Navigate to Profile (now positioned above Sign Out)"
echo "   4. Try uploading an avatar image"
echo "   5. Check that the loading state resolves properly"
echo "   6. Verify the improved glassmorphic styling"
echo ""
echo "📝 Process IDs (for manual cleanup if needed):"
echo "   Backend PID:  $BACKEND_PID"
echo "   Frontend PID: $FRONTEND_PID"
echo ""
echo "🛑 To stop servers: pkill -f 'python.*app.py' && pkill -f 'npm.*start'"
