rules_version = '2';

// Firebase Storage Security Rules for PTT E-commerce Dashboard
// These rules control access to files stored in Firebase Storage
service firebase.storage {
  match /b/{bucket}/o {
    
    // Avatar uploads - users can upload to their own directory
    // Path: avatars/{userId}/{filename}
    match /avatars/{userId}/{allPaths=**} {
      // Allow public read access for avatar display in UI
      allow read: if true;
      
      // Allow authenticated users to upload/update/delete their own avatars
      allow write: if request.auth != null 
                   && request.auth.uid == userId
                   && request.resource.size < 5 * 1024 * 1024 // 5MB limit
                   && request.resource.contentType.matches('image/.*'); // Only images
    }
    
    // Default deny all other paths
    match /{allPaths=**} {
      allow read, write: if false;
    }
  }
}
