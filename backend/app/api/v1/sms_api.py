"""
SMS Classification API Endpoints
RESTful API for Vietnamese bank SMS classification
"""
import os
import logging
from flask import Blueprint, request, jsonify, g
from app.middleware import auth_required
from app.services.sms_classifier import SMSClassifier

logger = logging.getLogger(__name__)

# Create blueprint
sms_api_bp = Blueprint('sms_api', __name__)

# Initialize classifier (singleton pattern)
_classifier = None

def get_classifier() -> SMSClassifier:
    """Get or create SMS classifier instance with API key rotation support"""
    global _classifier
    if _classifier is None:
        # Support both single and multiple API keys
        gemini_api_keys = os.getenv('GEMINI_API_KEYS')  # Comma-separated list
        gemini_api_key = os.getenv('GEMINI_API_KEY')    # Single key (backward compatibility)

        if gemini_api_keys:
            # Multiple keys from environment
            api_keys = [key.strip() for key in gemini_api_keys.split(',') if key.strip()]
        elif gemini_api_key:
            # Single key from environment
            api_keys = [gemini_api_key]
        else:
            api_keys = []

        _classifier = SMSClassifier(api_keys)
        logger.info(f"SMS Classifier initialized for API with {len(api_keys)} API keys")
    return _classifier


@sms_api_bp.route('/process', methods=['POST'])
@auth_required
def process_sms():
    """
    Process SMS message: detect, classify, and create payment record in one operation

    Request Body:
    {
        "message": "SMS text content",
        "reporter": "username (optional)",
        "force_ai": false (optional) - Force AI even if regex works,
        "use_ai": true (optional) - Enable/disable AI classification
    }

    Response:
    {
        "success": true,
        "is_bank_sms": true,
        "payment_created": true,
        "detection": {
            "is_sms": true,
            "confidence": 0.85,
            "indicators": ["amount_pattern", "company_pattern"]
        },
        "classification": {
            "success": true,
            "data": {
                "amount": 5083560,
                "sender": "CTY CO PHAN ACE MICRON",
                "reference": "hoa don 5053",
                "bank": "MBBANK",
                "confidence": 0.95
            },
            "processing_method": "regex"
        },
        "payment": {
            "id": 123,
            "customer": "CTY CO PHAN ACE MICRON",
            "amount": 5083560,
            "status": "unmapped"
        },
        "processing_time_ms": 45.2
    }
    """
    import time
    from app.services.payment_service import PaymentService

    start_time = time.time()

    try:
        # Validate request
        if not request.is_json:
            return jsonify({
                'success': False,
                'error': 'Request must be JSON'
            }), 400

        data = request.get_json()
        if not data:
            return jsonify({
                'success': False,
                'error': 'Request body is required'
            }), 400

        # Extract message
        message = data.get('message')
        if not message:
            return jsonify({
                'success': False,
                'error': 'Message field is required'
            }), 400

        if not isinstance(message, str) or not message.strip():
            return jsonify({
                'success': False,
                'error': 'Message must be a non-empty string'
            }), 400

        # Extract optional parameters
        reporter = data.get('reporter', getattr(g.current_user, 'email', 'unknown') if g.current_user else 'unknown')
        force_ai = data.get('force_ai', False)
        use_ai = data.get('use_ai', True)  # Default to True (AI enabled)

        # Get classifier
        classifier = get_classifier()

        # Step 1: Detect if message is bank SMS
        detection_result = classifier.is_bank_sms(message.strip())

        response = {
            'success': True,
            'is_bank_sms': detection_result.is_sms,
            'payment_created': False,
            'detection': detection_result.to_dict(),
            'processing_time_ms': (time.time() - start_time) * 1000
        }

        # Step 2: If not bank SMS, return early
        if not detection_result.is_sms:
            return jsonify(response), 200

        # Step 3: Classify SMS and extract payment information
        classification_result = classifier.classify_sms(message.strip(), force_ai=force_ai, use_ai=use_ai)
        response['classification'] = classification_result.to_dict()

        # Step 4: If classification failed, return with error
        if not classification_result.success:
            response['success'] = False
            response['error'] = classification_result.error_message or 'SMS classification failed'
            response['processing_time_ms'] = (time.time() - start_time) * 1000
            return jsonify(response), 400

        # Step 5: Create payment record with structured error handling
        payment_result = PaymentService.create_payment_from_sms(
            classification_result.payment_info,
            reporter,
            message.strip()
        )

        if payment_result.success:
            response['payment_created'] = True
            response['payment'] = payment_result.data.to_dict()
            logger.info(f"SMS processing successful: created payment {payment_result.data.id}")
        else:
            # Payment creation failed - return appropriate error response
            response['success'] = False
            response['error'] = payment_result.error.to_dict()
            response['processing_time_ms'] = (time.time() - start_time) * 1000
            return jsonify(response), payment_result.error.get_http_status()

        response['processing_time_ms'] = (time.time() - start_time) * 1000
        return jsonify(response), 200

    except Exception as e:
        logger.error(f"Error in SMS processing API: {e}")
        return jsonify({
            'success': False,
            'error': 'Internal server error',
            'details': str(e),
            'processing_time_ms': (time.time() - start_time) * 1000
        }), 500


@sms_api_bp.route('/api-keys/stats', methods=['GET'])
@auth_required
def get_api_key_stats():
    """
    Get API key usage statistics
    """
    try:
        classifier = get_classifier()
        stats = classifier.get_api_key_stats()

        return jsonify({
            'success': True,
            'data': stats
        }), 200

    except Exception as e:
        logger.error(f"Error getting API key stats: {e}")
        return jsonify({
            'success': False,
            'error': 'Internal server error',
            'details': str(e)
        }), 500


@sms_api_bp.route('/api-keys/reset-failed', methods=['POST'])
@auth_required
def reset_failed_api_keys():
    """
    Reset all failed API keys (useful when quotas reset)
    """
    try:
        classifier = get_classifier()
        classifier.reset_failed_api_keys()

        return jsonify({
            'success': True,
            'message': 'Failed API keys have been reset'
        }), 200

    except Exception as e:
        logger.error(f"Error resetting failed API keys: {e}")
        return jsonify({
            'success': False,
            'error': 'Internal server error',
            'details': str(e)
        }), 500


@sms_api_bp.route('/health', methods=['GET'])
def health_check():
    """
    Health check endpoint

    Response:
    {
        "success": true,
        "data": {
            "status": "healthy",
            "regex_available": true,
            "ai_available": true,
            "ai_connection_test": true,
            "supported_banks": ["MBBANK", "TECHCOMBANK", ...],
            "enhanced_bank_detection": true
        }
    }
    """
    try:
        classifier = get_classifier()
        health_status = classifier.get_health_status()
        supported_banks = classifier.get_supported_banks()

        return jsonify({
            'success': True,
            'data': {
                'status': 'healthy',
                **health_status,
                'supported_banks': supported_banks,
                'enhanced_bank_detection': True
            }
        }), 200

    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return jsonify({
            'success': False,
            'error': 'Health check failed',
            'details': str(e)
        }), 500


@sms_api_bp.route('/banks/detection-info', methods=['GET'])
@auth_required
def get_bank_detection_info():
    """
    Get detailed information about enhanced bank detection capabilities

    Response:
    {
        "success": true,
        "data": {
            "enhanced_detection_enabled": true,
            "banks": {
                "VIETINBANK": {
                    "structural_detection": true,
                    "patterns": ["So GD goc:", "CT TNHH", ...],
                    "confidence": "High"
                },
                ...
            }
        }
    }
    """
    try:
        classifier = get_classifier()
        enhanced_support = classifier.get_enhanced_bank_support()

        return jsonify({
            'success': True,
            'data': {
                'enhanced_detection_enabled': True,
                'banks': enhanced_support
            }
        }), 200

    except Exception as e:
        logger.error(f"Error getting bank detection info: {e}")
        return jsonify({
            'success': False,
            'error': 'Internal server error',
            'details': str(e)
        }), 500


@sms_api_bp.route('/test', methods=['GET'])
def test_endpoint():
    """
    Simple test endpoint with sample data using the new process endpoint
    """
    sample_sms = "CT DEN:910T25612TSNTKSE CTY CO PHAN ACE MICRON thanh toan 50asc(37) con lai cho CONG TY TNHH CONG NGHE KY THUAT PHU THAI theo hoa don 5053 +5,083,560"

    try:
        # Simulate the process endpoint logic for testing
        from app.services.payment_service import PaymentService
        import time

        start_time = time.time()
        classifier = get_classifier()

        # Step 1: Detect if message is bank SMS
        detection_result = classifier.is_bank_sms(sample_sms)

        response = {
            'success': True,
            'message': 'Test endpoint working',
            'sample_input': sample_sms,
            'is_bank_sms': detection_result.is_sms,
            'detection': detection_result.to_dict(),
            'payment_created': False
        }

        if detection_result.is_sms:
            # Step 2: Classify SMS
            classification_result = classifier.classify_sms(sample_sms)
            response['classification'] = classification_result.to_dict()

            if classification_result.success:
                # Step 3: Create payment record (test mode - don't actually create)
                response['test_payment_data'] = {
                    'customer': classification_result.payment_info.sender,
                    'amount': classification_result.payment_info.amount,
                    'reference': classification_result.payment_info.reference,
                    'bank': classification_result.payment_info.bank,
                    'confidence': classification_result.payment_info.confidence
                }

        response['processing_time_ms'] = (time.time() - start_time) * 1000
        return jsonify(response), 200

    except Exception as e:
        logger.error(f"Test endpoint error: {e}")
        return jsonify({
            'success': False,
            'error': 'Test failed',
            'details': str(e)
        }), 500


# Add web interface route
@sms_api_bp.route('/web-test', methods=['GET'])
def web_test_interface():
    """
    Web interface for testing SMS classification
    """
    from flask import render_template
    return render_template('sms_test.html')
