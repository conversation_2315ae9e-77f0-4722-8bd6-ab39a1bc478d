"""Main Routes - Backward Compatibility"""
from flask import Blueprint, render_template, request, jsonify, send_file, send_from_directory, g
from app.services import MisaService
from app.middleware import auth_required, optional_auth, get_current_user
from datetime import datetime
import logging

logger = logging.getLogger(__name__)

main_bp = Blueprint('main', __name__)


@main_bp.route('/')
def index():
    """Render main dashboard."""
    return render_template('index.html')


@main_bp.route('/misa-check')
def misa_check():
    """New Misa Check page with PTT components"""
    return render_template('index.html')


@main_bp.route('/src/<path:path>')
def serve_src(path):
    """Serve source files for React components"""
    return send_from_directory('src', path)


@main_bp.route('/get_cost', methods=['POST'])
@auth_required
def get_cost():
    """
    Product cost checking endpoint (Legacy API - Active)

    Used by: MISA CHECK frontend component
    Authentication: Required (Firebase ID token)
    Request: {"product_codes": ["CODE1", "CODE2"]}
    Response: Array of product cost objects

    Note: This is the primary endpoint used by the frontend.
    For new integrations, consider using /api/v1/misa/cost-check
    """
    try:
        data = request.get_json()
        if not data or 'product_codes' not in data:
            return jsonify([])
        
        # Clean product codes
        product_codes = MisaService.validate_product_codes(data['product_codes'])
        
        if not product_codes:
            return jsonify([])
        
        # Check costs
        results = MisaService.check_product_costs(product_codes)
        
        return jsonify(results)
        
    except Exception as e:
        logger.error(f"Error in legacy cost check: {e}")
        return jsonify([])


@main_bp.route('/upload_excel', methods=['POST'])
@auth_required
def upload_excel():
    """
    Excel file upload endpoint (Legacy API - Active)

    Used by: ExcelUploadSection component
    Authentication: Required (Firebase ID token)
    Request: FormData with 'excel-file' or 'file' field
    Response: CSV file download

    Note: This endpoint processes Excel files and returns CSV data.
    For new integrations, consider using /api/v1/misa/upload-excel
    """
    try:
        from app.services import FileService
        from flask import Response
        
        # Check for 'file' field (new) or 'excel-file' field (legacy)
        file = None
        if 'file' in request.files:
            file = request.files['file']
        elif 'excel-file' in request.files:
            file = request.files['excel-file']
        
        if not file:
            logger.warning("No file part in the request")
            return jsonify({'status': 'No file part'}), 400
        
        if file.filename == '' or not FileService.allowed_file(file.filename):
            logger.warning("No selected file or file not allowed")
            return jsonify({'status': 'No selected file or file not allowed'}), 400
        
        # Process file
        result = FileService.process_excel_upload(file)
        
        if result['success'] and result['response_type'] == 'csv':
            # Return CSV file download response
            return Response(
                result['csv_data'],
                mimetype='text/csv',
                headers={'Content-Disposition': f'attachment; filename={result["filename"]}'}
            )
        elif result['success']:
            return jsonify({'status': 'success', 'message': result['message']})
        else:
            return jsonify({'status': 'error', 'message': result['message']}), 500
            
    except Exception as e:
        logger.error(f"Error in upload_excel: {str(e)}", exc_info=True)
        return jsonify({'status': 'error', 'message': str(e)}), 500


@main_bp.route('/update_fixed_costs', methods=['POST'])
@auth_required
def update_fixed_costs():
    """
    Fixed costs CSV upload endpoint (Legacy API - Active)

    Used by: FixedCostsSection component
    Authentication: Required (Firebase ID token)
    Request: FormData with 'file' field (CSV format)
    Response: JSON success/error message

    Note: This endpoint updates fixed costs in Firestore.
    For new integrations, consider using /api/v1/misa/fixed-costs
    """
    try:
        from app.services import FileService
        
        if 'file' not in request.files:
            return jsonify({'error': 'No file part'}), 400
        
        file = request.files['file']
        if file.filename == '':
            return jsonify({'error': 'No selected file'}), 400
        
        if file and file.filename.endswith('.csv'):
            # Process fixed costs file
            result = FileService.process_fixed_costs_upload(file)
            
            if result['success']:
                return jsonify({'message': result['message']}), 200
            else:
                return jsonify({'error': result['message']}), 500
        else:
            return jsonify({'error': 'Invalid file format. Please upload a CSV file.'}), 400
            
    except Exception as e:
        logger.error(f"Error in update_fixed_costs: {e}")
        return jsonify({'error': str(e)}), 500


# Authentication Test Endpoints (for testing Phase 1)

@main_bp.route('/auth/test/public')
def test_public():
    """Test endpoint - no authentication required."""
    return jsonify({
        'message': 'This is a public endpoint',
        'authenticated': bool(get_current_user()),
        'user': get_current_user()
    })


@main_bp.route('/auth/test/protected')
@auth_required
def test_protected():
    """Test endpoint - authentication required."""
    return jsonify({
        'message': 'This is a protected endpoint',
        'user': g.current_user,
        'timestamp': str(datetime.utcnow())
    })


@main_bp.route('/auth/test/optional')
@optional_auth
def test_optional():
    """Test endpoint - optional authentication."""
    user = get_current_user()
    return jsonify({
        'message': 'This endpoint has optional authentication',
        'authenticated': bool(user),
        'user': user
    })


@main_bp.route('/test-auth')
def test_auth_page():
    """Test page for Firebase authentication setup."""
    return send_from_directory('../../', 'test-auth.html')


@main_bp.route('/auth/user')
@auth_required
def get_current_user_profile():
    """Get current authenticated user information."""
    from app.services.firebase_service import FirebaseService

    try:
        # Get user profile from Firestore
        profile = FirebaseService.get_user_profile(g.current_user['uid'])

        return jsonify({
            'user': g.current_user,
            'profile': profile,
            'authenticated': True
        })
    except Exception as e:
        logger.error(f"Error getting user profile: {e}")
        return jsonify({
            'user': g.current_user,
            'profile': None,
            'authenticated': True
        })


@main_bp.route('/auth/user/profile', methods=['PUT'])
@auth_required
def update_user_profile():
    """Update user profile information."""
    from app.services.firebase_service import FirebaseService

    try:
        data = request.get_json()

        if not data:
            return jsonify({
                'success': False,
                'message': 'No data provided'
            }), 400

        # Validate nickname if provided
        nickname = data.get('nickname', '').strip()
        if nickname:
            if len(nickname) < 2 or len(nickname) > 50:
                return jsonify({
                    'success': False,
                    'message': 'Nickname must be between 2 and 50 characters'
                }), 400

            # Check for valid characters (alphanumeric, spaces, hyphens, underscores)
            import re
            if not re.match(r'^[a-zA-Z0-9\s\-_]+$', nickname):
                return jsonify({
                    'success': False,
                    'message': 'Nickname can only contain letters, numbers, spaces, hyphens, and underscores'
                }), 400

        # Prepare update data
        update_data = {}
        if nickname:
            update_data['display_name'] = nickname

        if 'photo_url' in data:
            update_data['photo_url'] = data['photo_url']

        # Update profile in Firestore
        success = FirebaseService.update_user_profile_fields(g.current_user['uid'], update_data)

        if success:
            # Get updated profile
            updated_profile = FirebaseService.get_user_profile(g.current_user['uid'])
            return jsonify({
                'success': True,
                'message': 'Profile updated successfully',
                'profile': updated_profile
            })
        else:
            return jsonify({
                'success': False,
                'message': 'Failed to update profile'
            }), 500

    except Exception as e:
        logger.error(f"Error updating user profile: {e}")
        return jsonify({
            'success': False,
            'message': 'Internal server error'
        }), 500


@main_bp.route('/dashboard')
def dashboard():
    """Dashboard page."""
    return render_template('dashboard.html')


@main_bp.route('/sms-test')
def sms_test():
    """SMS Classification Test Page."""
    return render_template('sms_test.html')


@main_bp.route('/chat-test')
def chat_test():
    """Real-time Chat Test Page."""
    return render_template('chat_test.html')


@main_bp.route('/health')
def health_check():
    """Simple health check endpoint."""
    return jsonify({
        'status': 'healthy',
        'message': 'PTT E-commerce Dashboard is running'
    })


@main_bp.errorhandler(404)
def not_found(error):
    """Handle 404 errors."""
    return render_template('error.html', error_code=404, error_message='Page not found'), 404


@main_bp.errorhandler(500)
def internal_error(error):
    """Handle 500 errors."""
    logger.error(f"Internal server error: {error}")
    return render_template('error.html', error_code=500, error_message='Internal server error'), 500 