"""
Google Gemini AI Classifier for Vietnamese Bank SMS
Uses Gemini Flash 2.5 Lite for intelligent SMS parsing when regex fails
Enhanced with API key rotation for quota management
"""
import os
import json
import logging
import random
import time
from typing import Optional, Dict, Any, List
import google.generativeai as genai
from .sms_models import PaymentInfo

logger = logging.getLogger(__name__)


class APIKeyRotator:
    """
    Manages rotation of multiple API keys to avoid quota limits
    """

    def __init__(self, api_keys: List[str]):
        """Initialize with list of API keys"""
        self.api_keys = api_keys if api_keys else []
        self.current_index = 0
        self.failed_keys = set()  # Track temporarily failed keys
        self.key_usage_count = {}  # Track usage per key
        self.key_last_used = {}   # Track last usage time per key
        self.max_retries_per_key = 3

        # Initialize usage tracking
        for key in self.api_keys:
            self.key_usage_count[key] = 0
            self.key_last_used[key] = 0

        logger.info(f"API Key Rotator initialized with {len(self.api_keys)} keys")

    def get_next_key(self) -> Optional[str]:
        """Get the next available API key"""
        if not self.api_keys:
            return None

        # Filter out failed keys
        available_keys = [key for key in self.api_keys if key not in self.failed_keys]

        if not available_keys:
            # Reset failed keys if all are failed (quota might have reset)
            logger.warning("All API keys failed, resetting failed keys list")
            self.failed_keys.clear()
            available_keys = self.api_keys

        # Use round-robin with usage balancing
        if available_keys:
            # Sort by usage count (least used first)
            available_keys.sort(key=lambda k: self.key_usage_count.get(k, 0))
            selected_key = available_keys[0]

            # Update usage tracking
            self.key_usage_count[selected_key] += 1
            self.key_last_used[selected_key] = time.time()

            logger.debug(f"Selected API key (usage: {self.key_usage_count[selected_key]})")
            return selected_key

        return None

    def mark_key_failed(self, api_key: str, error_message: str = ""):
        """Mark an API key as temporarily failed"""
        if api_key in self.api_keys:
            self.failed_keys.add(api_key)
            logger.warning(f"Marked API key as failed: {error_message}")

    def reset_failed_keys(self):
        """Reset all failed keys (useful when quotas reset)"""
        self.failed_keys.clear()
        logger.info("Reset all failed API keys")

    def get_stats(self) -> Dict[str, Any]:
        """Get usage statistics"""
        return {
            'total_keys': len(self.api_keys),
            'available_keys': len(self.api_keys) - len(self.failed_keys),
            'failed_keys': len(self.failed_keys),
            'usage_per_key': {f"key_{i+1}": count for i, count in enumerate(self.key_usage_count.values())}
        }


class GeminiAIClassifier:
    """
    AI-powered SMS classifier using Google Gemini Flash 2.5 Lite
    Enhanced with API key rotation for quota management
    """

    def __init__(self, api_keys: Optional[List[str]] = None):
        """Initialize Gemini AI classifier with API key rotation"""
        # Handle both single key and multiple keys
        if api_keys is None:
            # Try to get from environment - support both single and multiple keys
            env_key = os.getenv('GEMINI_API_KEY')
            env_keys = os.getenv('GEMINI_API_KEYS')  # Comma-separated list

            if env_keys:
                # Multiple keys from environment
                api_keys = [key.strip() for key in env_keys.split(',') if key.strip()]
            elif env_key:
                # Single key from environment
                api_keys = [env_key]
            else:
                api_keys = []

        # Initialize key rotator
        self.key_rotator = APIKeyRotator(api_keys)
        self.model = None
        self.current_api_key = None
        self.is_initialized = False

        # Try to initialize with first available key
        self._initialize_with_next_key()

    def _initialize_with_next_key(self) -> bool:
        """Initialize with the next available API key"""
        api_key = self.key_rotator.get_next_key()

        if not api_key:
            logger.warning("No API keys available for initialization")
            return False

        try:
            genai.configure(api_key=api_key)
            # Use Gemini Flash 2.5 Lite for cost efficiency
            self.model = genai.GenerativeModel('gemini-2.0-flash-exp')
            self.current_api_key = api_key
            self.is_initialized = True
            logger.info("Gemini AI classifier initialized successfully with key rotation")
            return True
        except Exception as e:
            logger.error(f"Failed to initialize Gemini AI with current key: {e}")
            self.key_rotator.mark_key_failed(api_key, str(e))
            self.is_initialized = False
            return False
    
    def is_available(self) -> bool:
        """Check if AI classifier is available"""
        return self.is_initialized and self.model is not None and self.key_rotator.api_keys

    def classify_sms(self, sms_text: str) -> PaymentInfo:
        """
        Classify SMS using Gemini AI with automatic key rotation on failures
        Returns: PaymentInfo object
        """
        if not self.key_rotator.api_keys:
            logger.warning("No API keys available for Gemini AI")
            return PaymentInfo(confidence=0.0)

        max_attempts = min(len(self.key_rotator.api_keys), 3)  # Try up to 3 keys

        for attempt in range(max_attempts):
            if not self.is_available():
                # Try to initialize with next key
                if not self._initialize_with_next_key():
                    continue

            try:
                prompt = self._create_prompt(sms_text)
                response = self.model.generate_content(prompt)

                if response and response.text:
                    result = self._parse_ai_response(response.text, sms_text)
                    logger.debug(f"AI classification successful on attempt {attempt + 1}")
                    return result
                else:
                    logger.warning("Empty response from Gemini AI")
                    return PaymentInfo(confidence=0.0)

            except Exception as e:
                error_msg = str(e).lower()

                # Check if it's a quota/rate limit error
                if any(keyword in error_msg for keyword in ['quota', 'rate limit', 'limit exceeded', 'too many requests']):
                    logger.warning(f"Quota/rate limit hit on attempt {attempt + 1}: {e}")

                    # Mark current key as failed and try next one
                    if self.current_api_key:
                        self.key_rotator.mark_key_failed(self.current_api_key, str(e))

                    # Reset initialization to force key rotation
                    self.is_initialized = False
                    self.current_api_key = None

                    # Continue to next attempt with different key
                    continue
                else:
                    # Non-quota error, return immediately
                    logger.error(f"Error in Gemini AI classification: {e}")
                    return PaymentInfo(confidence=0.0)

        # All attempts failed
        logger.error("All API key attempts failed for Gemini AI classification")
        return PaymentInfo(confidence=0.0)

    def get_api_key_stats(self) -> Dict[str, Any]:
        """Get API key usage statistics"""
        return self.key_rotator.get_stats()

    def reset_failed_keys(self):
        """Reset all failed API keys (useful when quotas reset)"""
        self.key_rotator.reset_failed_keys()
    
    def _create_prompt(self, sms_text: str) -> str:
        """Create optimized prompt for Gemini AI with Vietnamese bank pattern recognition"""
        prompt = f"""
You are an expert at parsing Vietnamese bank SMS notifications. Extract payment information from this SMS text.

SMS Text: "{sms_text}"

IMPORTANT: Vietnamese Bank Pattern Recognition:
- TECHCOMBANK: Look for "MBVCB." codes, "FT" references, "VIETCOMBANK Hanoi VNM" source, combined date-time format "DD/MM/YYYY HH:MM:SS"
- VIETCOMBANK: Look for "IBFPM." codes, "YYYY-MM-DD" date format, "VND" currency mentions
- VIETINBANK: Look for "So GD goc:" patterns, separate "DD/MM/YYYY" and "HH:MM:SS" formats, 17-digit account numbers

Extract the following information and return ONLY a valid JSON object:

{{
    "amount": <number in VND without commas, null if not found>,
    "sender": "<company or person name, null if not found>",
    "reference": "<invoice/reference number, null if not found>",
    "bank": "<bank name - use TECHCOMBANK, VIETCOMBANK, VIETINBANK, MBBANK, VCB, BIDV, ACB, SHINHAN, null if not found>",
    "confidence": <float between 0.0 and 1.0>
}}

Bank Detection Rules:
1. If you see "MBVCB." anywhere → bank is "TECHCOMBANK" (high confidence)
2. If you see "IBFPM." anywhere → bank is "VIETCOMBANK" (high confidence)
3. If you see "So GD goc:" anywhere → bank is "VIETINBANK" (high confidence)
4. If you see "VIETCOMBANK Hanoi VNM" → likely "TECHCOMBANK" (this is a source pattern)
5. Combined date-time "DD/MM/YYYY HH:MM:SS" → likely "TECHCOMBANK"
6. ISO date "YYYY-MM-DD" → likely "VIETCOMBANK"

Other Rules:
1. Amount should be a number without commas (e.g., 5083560 not "5,083,560")
2. Extract company names like "CTY CO PHAN ACE MICRON" or "CONG TY TNHH ISHISEI VIET NAM"
3. Look for reference numbers after "hoa don", "HD", "BG", "FT", etc.
4. Set confidence based on how clear the information is
5. Return ONLY the JSON object, no other text

Example output:
{{"amount": 5083560, "sender": "CTY CO PHAN ACE MICRON", "reference": "hoa don 5053", "bank": "MBBANK", "confidence": 0.95}}
"""
        return prompt
    
    def _parse_ai_response(self, response_text: str, original_sms: str) -> PaymentInfo:
        """
        Parse AI response and convert to PaymentInfo
        """
        try:
            # Clean the response text
            response_text = response_text.strip()
            
            # Try to extract JSON from response
            if response_text.startswith('```json'):
                response_text = response_text[7:]
            if response_text.endswith('```'):
                response_text = response_text[:-3]
            
            response_text = response_text.strip()
            
            # Parse JSON
            data = json.loads(response_text)
            
            # Validate and create PaymentInfo with fallback for missing sender
            sender = data.get('sender')
            if not sender or sender.strip() == "":
                sender = "Cannot Detect"
                logger.debug(f"AI: Using fallback sender 'Cannot Detect'")

            return PaymentInfo(
                amount=data.get('amount'),
                sender=sender,
                reference=data.get('reference'),
                bank=data.get('bank'),
                confidence=min(max(data.get('confidence', 0.0), 0.0), 1.0),
                raw_data={
                    'ai_response': data,
                    'original_sms': original_sms
                }
            )
            
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse AI response as JSON: {e}")
            logger.error(f"Response text: {response_text}")
            return PaymentInfo(confidence=0.0)
        except Exception as e:
            logger.error(f"Error parsing AI response: {e}")
            return PaymentInfo(confidence=0.0)
    
    def test_connection(self) -> bool:
        """Test if AI service is working"""
        if not self.is_available():
            return False
        
        try:
            test_prompt = "Return only this JSON: {\"test\": true}"
            response = self.model.generate_content(test_prompt)
            return response and response.text and 'test' in response.text
        except Exception as e:
            logger.error(f"AI connection test failed: {e}")
            return False
