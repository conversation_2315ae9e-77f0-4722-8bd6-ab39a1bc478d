"""Firebase Service - Database Operations and Authentication"""
import firebase_admin
from firebase_admin import credentials, firestore, auth
from flask import current_app
import logging
from datetime import datetime

logger = logging.getLogger(__name__)


class FirebaseService:
    """Service for Firebase Firestore operations."""
    
    _db = None
    _initialized = False
    
    @classmethod
    def initialize(cls, config_path=None):
        """Initialize Firebase app and Firestore client."""
        if cls._initialized:
            return cls._db
            
        try:
            # Initialize Firebase
            if config_path:
                firebase_key_path = config_path
            else:
                # Try to get from Flask context, fallback to direct path
                try:
                    firebase_key_path = current_app.config['FIREBASE_KEY_PATH']
                except RuntimeError:
                    # No Flask context, use fallback path
                    firebase_key_path = 'CheckMisa_Firebase_Key.json'
            
            cred = credentials.Certificate(firebase_key_path)
            firebase_admin.initialize_app(cred)
            cls._db = firestore.client()
            cls._initialized = True
            logger.info("Firebase initialized successfully")
            return cls._db
        except Exception as e:
            logger.error(f"Firebase initialization failed: {e}")
            raise
    
    @classmethod
    def get_db(cls):
        """Get Firestore database client."""
        if not cls._initialized:
            cls.initialize()
        return cls._db
    
    @classmethod
    def get_fixed_costs(cls):
        """Get fixed costs from Firestore."""
        try:
            db = cls.get_db()
            fixed_costs_ref = db.collection('fixed_costs')
            docs = fixed_costs_ref.stream()
            
            fixed_costs = {}
            for doc in docs:
                data = doc.to_dict()
                fixed_costs[doc.id] = data
            
            return fixed_costs
        except Exception as e:
            logger.error(f"Error getting fixed costs: {e}")
            return {}
    
    @classmethod
    def batch_get_documents(cls, collection_ref, product_codes):
        """EXACT REPLICA of original batch_get_documents function."""
        docs = []

        # Firestore allows max 10 'in' clauses per query, so query in batches of 10
        for i in range(0, len(product_codes), 10):
            chunk = product_codes[i:i + 10]
            query = collection_ref.where('product_code', 'in', chunk).stream()
            docs.extend(query)  # `stream()` is synchronous

        return docs

    @classmethod
    def get_batch_data(cls, normalized_product_codes):
        """Get batch data exactly like original function."""
        try:
            db = cls.get_db()
            fixed_costs_ref = db.collection('fixed_costs')
            stock_ref = db.collection('stock_data')

            fixed_costs_docs = cls.batch_get_documents(fixed_costs_ref, normalized_product_codes)
            stock_docs = cls.batch_get_documents(stock_ref, normalized_product_codes)

            fixed_costs_dict = {doc.get('product_code').strip(): doc.to_dict() for doc in fixed_costs_docs}
            stock_dict = {doc.get('product_code').strip(): doc.to_dict() for doc in stock_docs}

            return fixed_costs_dict, stock_dict
        except Exception as e:
            logger.error(f"Error getting batch data: {e}")
            return {}, {}
    
    @classmethod
    def update_fixed_costs(cls, cost_data):
        """Update fixed costs in Firestore."""
        try:
            db = cls.get_db()
            
            for product_code, data in cost_data.items():
                doc_ref = db.collection('fixed_costs').document(product_code)
                doc_ref.set(data, merge=True)
            
            logger.info(f"Updated fixed costs for {len(cost_data)} products")
            return True
        except Exception as e:
            logger.error(f"Error updating fixed costs: {e}")
            return False
    
    # Authentication Methods
    
    @classmethod
    def verify_id_token(cls, id_token):
        """
        Verify Firebase ID token and return decoded claims.
        
        Args:
            id_token (str): Firebase ID token from client
            
        Returns:
            dict: Decoded token claims
            
        Raises:
            Exception: If token verification fails
        """
        try:
            decoded_token = auth.verify_id_token(id_token)
            return decoded_token
        except Exception as e:
            logger.error(f"ID token verification failed: {e}")
            raise
    
    @classmethod
    def get_user_by_uid(cls, uid):
        """
        Get user information by Firebase UID.
        
        Args:
            uid (str): Firebase user UID
            
        Returns:
            dict: User information
        """
        try:
            user_record = auth.get_user(uid)
            return {
                'uid': user_record.uid,
                'email': user_record.email,
                'display_name': user_record.display_name,
                'photo_url': user_record.photo_url,
                'email_verified': user_record.email_verified,
                'disabled': user_record.disabled,
                'creation_time': user_record.user_metadata.creation_timestamp,
                'last_sign_in_time': user_record.user_metadata.last_sign_in_timestamp
            }
        except Exception as e:
            logger.error(f"Error getting user by UID {uid}: {e}")
            return None
    
    @classmethod
    def create_or_update_user_profile(cls, uid, user_data):
        """
        Create or update user profile in Firestore.
        
        Args:
            uid (str): Firebase user UID
            user_data (dict): User profile data
            
        Returns:
            bool: Success status
        """
        try:
            db = cls.get_db()
            user_ref = db.collection('users').document(uid)
            
            # Get existing user data
            existing_user = user_ref.get()
            
            # Prepare user profile data
            profile_data = {
                'uid': uid,
                'email': user_data.get('email'),
                'display_name': user_data.get('name') or user_data.get('display_name'),
                'photo_url': user_data.get('picture') or user_data.get('photo_url'),
                'email_verified': user_data.get('email_verified', False),
                'updated_at': datetime.utcnow(),
                'last_login': datetime.utcnow()
            }
            
            # If user doesn't exist, add creation timestamp
            if not existing_user.exists:
                profile_data['created_at'] = datetime.utcnow()
                profile_data['is_active'] = True
                profile_data['role'] = 'user'  # Default role
                logger.info(f"Creating new user profile: {uid}")
            else:
                logger.debug(f"Updating existing user profile: {uid}")
            
            # Save to Firestore
            user_ref.set(profile_data, merge=True)
            return True
            
        except Exception as e:
            logger.error(f"Error creating/updating user profile for {uid}: {e}")
            return False
    
    @classmethod
    def get_user_profile(cls, uid):
        """
        Get user profile from Firestore.
        
        Args:
            uid (str): Firebase user UID
            
        Returns:
            dict: User profile data or None if not found
        """
        try:
            db = cls.get_db()
            user_ref = db.collection('users').document(uid)
            user_doc = user_ref.get()
            
            if user_doc.exists:
                return user_doc.to_dict()
            return None
            
        except Exception as e:
            logger.error(f"Error getting user profile for {uid}: {e}")
            return None
    
    @classmethod
    def update_user_last_login(cls, uid):
        """
        Update user's last login timestamp.
        
        Args:
            uid (str): Firebase user UID
        """
        try:
            db = cls.get_db()
            user_ref = db.collection('users').document(uid)
            user_ref.update({
                'last_login': datetime.utcnow()
            })
            logger.debug(f"Updated last login for user: {uid}")
        except Exception as e:
            logger.error(f"Error updating last login for {uid}: {e}")
    
    @classmethod
    def is_user_active(cls, uid):
        """
        Check if user is active.

        Args:
            uid (str): Firebase user UID

        Returns:
            bool: True if user is active, False otherwise
        """
        try:
            profile = cls.get_user_profile(uid)
            return profile.get('is_active', False) if profile else False
        except Exception as e:
            logger.error(f"Error checking user active status for {uid}: {e}")
            return False

    @classmethod
    def update_user_profile_fields(cls, uid, update_data):
        """
        Update specific fields in user profile.

        Args:
            uid (str): Firebase user UID
            update_data (dict): Fields to update

        Returns:
            bool: Success status
        """
        try:
            db = cls.get_db()
            user_ref = db.collection('users').document(uid)

            # Add updated timestamp
            update_data['updated_at'] = datetime.utcnow()

            # Update the document
            user_ref.update(update_data)
            logger.info(f"Updated user profile fields for {uid}: {list(update_data.keys())}")
            return True

        except Exception as e:
            logger.error(f"Error updating user profile fields for {uid}: {e}")
            return False