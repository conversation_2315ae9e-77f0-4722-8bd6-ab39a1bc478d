"""
Main SMS Classifier Service
Combines regex patterns with AI fallback for Vietnamese bank SMS classification
"""
import re
import time
import logging
from typing import Optional, List
from .sms_models import PaymentInfo, SMSClassificationResult, SMSDetectionResult
from .vietnamese_regex_patterns import VietnameseBankRegexPatterns
from .gemini_ai_classifier import GeminiAIClassifier

logger = logging.getLogger(__name__)


class SMSClassifier:
    """
    Main SMS classifier that intelligently routes between regex and AI
    Enhanced with API key rotation support
    """

    def __init__(self, gemini_api_keys: Optional[List[str]] = None):
        """Initialize SMS classifier with regex and AI components"""
        self.regex_patterns = VietnameseBankRegexPatterns()

        # Support both single key (backward compatibility) and multiple keys
        if isinstance(gemini_api_keys, str):
            # Single key provided (backward compatibility)
            gemini_api_keys = [gemini_api_keys]

        self.ai_classifier = GeminiAIClassifier(gemini_api_keys)

        # Configuration
        self.regex_confidence_threshold = 0.7
        self.sms_detection_threshold = 0.6

        logger.info("SMS Classifier initialized with API key rotation support")
    
    def is_bank_sms(self, message: str) -> SMSDetectionResult:
        """
        Detect if a message is a bank SMS notification
        Returns: SMSDetectionResult
        """
        if not message or not message.strip():
            return SMSDetectionResult(False, 0.0)
        
        message_upper = message.upper().strip()
        indicators = []
        confidence_score = 0.0
        
        # Check for amount indicators
        amount_indicators = [
            r'\+[\d,]+',  # +5,083,560
            r'[\d,]+\s*VND',  # 5,083,560 VND
            r'So\s+tien',  # So tien GD
            r'GD:\+[\d,]+',  # GD:+195,051,040
            r'[\d,]{7,}',  # Large numbers with commas (7+ digits)
        ]
        
        for pattern in amount_indicators:
            if re.search(pattern, message_upper):
                indicators.append(f"amount_pattern: {pattern}")
                confidence_score += 0.3
        
        # Check for company indicators
        company_indicators = [
            r'CTY\s+CO\s+PHAN',
            r'CONG\s+TY\s+TNHH',
            r'CONG\s+TY\s+CP',
        ]
        
        for pattern in company_indicators:
            if re.search(pattern, message_upper):
                indicators.append(f"company_pattern: {pattern}")
                confidence_score += 0.2
        
        # Check for bank indicators (both name-based and structural)
        bank_indicators = [
            'MBBANK', 'TECHCOMBANK', 'VCB', 'VIETCOMBANK',
            'BIDV', 'ACB', 'VIETINBANK', 'SHINHAN'
        ]

        for bank in bank_indicators:
            if bank in message_upper:
                indicators.append(f"bank: {bank}")
                confidence_score += 0.2

        # Check for bank-specific structural indicators
        bank_structural_indicators = [
            # Vietinbank indicators
            (r'SO\s+GD\s+GOC:', 'vietinbank_unique'),  # Case-insensitive pattern
            (r'^\d{2}/\d{2}/\d{4}$', 'vietinbank_date_format'),
            (r'^\d{2}:\d{2}:\d{2}$', 'vietinbank_time_format'),
            # Vietcombank indicators
            (r'IBFPM\.', 'vietcombank_unique'),  # Escaped dot
            (r'^\d{4}-\d{2}-\d{2}$', 'vietcombank_date_format'),
            (r'\s+VND', 'vietcombank_currency'),  # More specific VND pattern
            # Techcombank indicators
            (r'MBVCB\.', 'techcombank_unique'),  # Escaped dot
            (r'\d{2}/\d{2}/\d{4}\s+\d{2}:\d{2}:\d{2}', 'techcombank_datetime_format'),
            (r'FT\d+', 'techcombank_reference'),
            (r'VIETCOMBANK\s+HANOI\s+VNM', 'techcombank_source'),
        ]

        for pattern, indicator_name in bank_structural_indicators:
            if re.search(pattern, message_upper, re.MULTILINE):
                indicators.append(f"bank_structure: {indicator_name}")
                # Higher weight for unique identifiers and datetime patterns
                if 'unique' in indicator_name or 'format' in indicator_name:
                    confidence_score += 0.3
                else:
                    confidence_score += 0.2
        
        # Check for transaction indicators
        transaction_indicators = [
            'THANH TOAN', 'HOA DON', 'CHUYEN TIEN', 'NHAN TIEN',
            'DAT COC', 'TT TIEN', 'CT DEN', 'TU ', 'GD:'
        ]
        
        for indicator in transaction_indicators:
            if indicator in message_upper:
                indicators.append(f"transaction: {indicator}")
                confidence_score += 0.15
        
        # Normalize confidence score
        confidence_score = min(confidence_score, 1.0)
        is_sms = confidence_score >= self.sms_detection_threshold
        
        return SMSDetectionResult(is_sms, confidence_score, indicators)
    
    def classify_sms(self, sms_text: str, force_ai: bool = False, use_ai: bool = True) -> SMSClassificationResult:
        """
        Classify SMS message and extract payment information

        Args:
            sms_text: Raw SMS text
            force_ai: Force AI classification even if regex works
            use_ai: Enable/disable AI classification (default: True)

        Returns:
            SMSClassificationResult with extracted payment info
        """
        start_time = time.time()
        
        try:
            # Validate input
            if not sms_text or not sms_text.strip():
                return SMSClassificationResult(
                    success=False,
                    error_message="Empty SMS text",
                    processing_time_ms=(time.time() - start_time) * 1000
                )
            
            # Step 1: Detect if this is a bank SMS
            detection_result = self.is_bank_sms(sms_text)
            if not detection_result.is_sms:
                return SMSClassificationResult(
                    success=False,
                    error_message="Message does not appear to be a bank SMS",
                    processing_method="detection",
                    processing_time_ms=(time.time() - start_time) * 1000
                )
            
            # Step 2: Try regex extraction first
            regex_result = self.regex_patterns.extract_all_info(sms_text)

            # Log basic extraction results
            logger.info(f"Regex extraction - Amount: {regex_result.amount}, Sender: {regex_result.sender}, Confidence: {regex_result.confidence:.2f}")

            # Step 3: Decide whether to use AI fallback
            # Use AI when: confidence is low OR required fields are missing
            has_required_fields = regex_result.has_required_fields()
            confidence_low = regex_result.confidence < self.regex_confidence_threshold

            should_use_ai = (
                use_ai and (  # Only if AI is enabled
                    force_ai or
                    confidence_low or
                    not has_required_fields  # Missing amount or sender
                )
            )

            # Log fallback decision
            if should_use_ai and not force_ai:
                reasons = []
                if confidence_low:
                    reasons.append(f"low confidence ({regex_result.confidence:.2f} < {self.regex_confidence_threshold})")
                if not has_required_fields:
                    reasons.append("missing required fields (amount or sender)")
                logger.info(f"Using AI fallback: {', '.join(reasons)}")
            elif force_ai:
                logger.info("Using AI: forced by user")

            if should_use_ai and self.ai_classifier.is_available():
                # Use AI classification
                ai_result = self.ai_classifier.classify_sms(sms_text)
                logger.info(f"AI extraction - Amount: {ai_result.amount}, Sender: {ai_result.sender}, Bank: {ai_result.bank}, Confidence: {ai_result.confidence:.2f}")

                # CRITICAL FIX: Preserve high-confidence structural bank detection from regex
                regex_bank_confidence = regex_result.raw_data.get('bank_confidence', 0.0) if regex_result.raw_data else 0.0
                preserve_regex_bank = (
                    regex_result.bank and
                    regex_bank_confidence > 0.8 and
                    regex_result.bank in ['TECHCOMBANK', 'VIETCOMBANK', 'VIETINBANK']
                )

                if preserve_regex_bank:
                    logger.info(f"🏦 PRESERVING high-confidence regex bank detection: {regex_result.bank} (confidence: {regex_bank_confidence:.3f})")

                # Enhanced hybrid logic with bank preservation
                if ai_result.confidence > regex_result.confidence:
                    final_result = ai_result
                    processing_method = "ai"

                    # ALWAYS preserve high-confidence structural bank detection from regex
                    if preserve_regex_bank:
                        final_result.bank = regex_result.bank
                        logger.info(f"🔒 FORCED bank preservation: {regex_result.bank} (overriding AI: {ai_result.bank})")
                    # Otherwise, preserve bank information from regex if AI didn't detect it
                    elif not final_result.bank and regex_result.bank:
                        final_result.bank = regex_result.bank
                        logger.info(f"Merged bank info from regex: {regex_result.bank}")

                    # Preserve reference from regex if AI didn't detect it
                    if not final_result.reference and regex_result.reference:
                        final_result.reference = regex_result.reference
                        logger.info(f"Merged reference from regex: {regex_result.reference}")

                    logger.info(f"Using AI result (confidence {ai_result.confidence:.2f} > {regex_result.confidence:.2f}) with regex enhancements")
                else:
                    final_result = regex_result
                    processing_method = "hybrid"
                    logger.info(f"Using regex result (confidence {regex_result.confidence:.2f} >= {ai_result.confidence:.2f})")
            else:
                # Use regex result
                final_result = regex_result
                processing_method = "regex"
                if not should_use_ai:
                    logger.info(f"Using regex-only result (AI not needed)")
                else:
                    logger.info(f"Using regex result (AI not available)")
            
            # Validate final result
            if not final_result.is_valid():
                logger.warning(f"SMS validation failed - Amount: {final_result.amount}, Sender: {final_result.sender}, Confidence: {final_result.confidence:.2f}")
                return SMSClassificationResult(
                    success=False,
                    error_message="Could not extract valid payment information",
                    processing_method=processing_method,
                    processing_time_ms=(time.time() - start_time) * 1000
                )
            
            return SMSClassificationResult(
                success=True,
                payment_info=final_result,
                processing_method=processing_method,
                processing_time_ms=(time.time() - start_time) * 1000
            )
            
        except Exception as e:
            logger.error(f"Error in SMS classification: {e}")
            return SMSClassificationResult(
                success=False,
                error_message=f"Classification error: {str(e)}",
                processing_time_ms=(time.time() - start_time) * 1000
            )
    
    def batch_classify(self, sms_list: List[str]) -> List[SMSClassificationResult]:
        """
        Classify multiple SMS messages
        
        Args:
            sms_list: List of SMS texts
            
        Returns:
            List of SMSClassificationResult
        """
        results = []
        for sms in sms_list:
            result = self.classify_sms(sms)
            results.append(result)
        return results
    
    def get_health_status(self) -> dict:
        """Get health status of the classifier"""
        return {
            'regex_available': True,
            'ai_available': self.ai_classifier.is_available(),
            'confidence_threshold': self.regex_confidence_threshold,
            'detection_threshold': self.sms_detection_threshold,
            'api_key_stats': self.ai_classifier.get_api_key_stats()
        }

    def get_api_key_stats(self) -> dict:
        """Get API key usage statistics"""
        return self.ai_classifier.get_api_key_stats()

    def reset_failed_api_keys(self):
        """Reset all failed API keys (useful when quotas reset)"""
        self.ai_classifier.reset_failed_keys()

    def get_supported_banks(self) -> List[str]:
        """Get list of supported banks with enhanced detection"""
        return [
            'MBBANK', 'TECHCOMBANK', 'VCB', 'VIETCOMBANK',
            'BIDV', 'ACB', 'VIETINBANK', 'SHINHAN'
        ]

    def get_enhanced_bank_support(self) -> dict:
        """Get detailed information about enhanced bank detection support"""
        return {
            'VIETINBANK': {
                'structural_detection': True,
                'patterns': ['So GD goc: (unique)', 'DD/MM/YYYY date format', 'HH:MM:SS time format', '17-digit accounts'],
                'confidence': 'High',
                'reliability': 'Very High - Date/time format + unique identifiers'
            },
            'VIETCOMBANK': {
                'structural_detection': True,
                'patterns': ['IBFPM. (unique)', 'YYYY-MM-DD date format', 'VND currency', 'transaction codes'],
                'confidence': 'High',
                'reliability': 'Very High - ISO date format + unique identifiers'
            },
            'TECHCOMBANK': {
                'structural_detection': True,
                'patterns': ['MBVCB. (unique)', 'DD/MM/YYYY HH:MM:SS combined format', 'FT references', 'VIETCOMBANK Hanoi VNM'],
                'confidence': 'High',
                'reliability': 'Very High - Combined datetime format + unique identifiers'
            },
            'MBBANK': {
                'structural_detection': False,
                'patterns': ['Name-based detection only'],
                'confidence': 'Medium'
            },
            'VCB': {
                'structural_detection': False,
                'patterns': ['Name-based detection only'],
                'confidence': 'Medium'
            },
            'BIDV': {
                'structural_detection': False,
                'patterns': ['Name-based detection only'],
                'confidence': 'Medium'
            },
            'ACB': {
                'structural_detection': False,
                'patterns': ['Name-based detection only'],
                'confidence': 'Medium'
            },
            'SHINHAN': {
                'structural_detection': False,
                'patterns': ['Name-based detection only'],
                'confidence': 'Medium'
            }
        }
