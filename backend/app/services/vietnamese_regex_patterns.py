"""
Vietnamese Bank SMS Regex Patterns
Optimized patterns for extracting payment information from Vietnamese bank SMS
"""
import re
import logging
from typing import Dict, Optional, List, Tuple
from .sms_models import PaymentInfo, BankType

logger = logging.getLogger(__name__)


class VietnameseBankRegexPatterns:
    """
    Regex patterns for extracting information from Vietnamese bank SMS messages
    """
    
    def __init__(self):
        self.amount_patterns = [
            # +5,083,560 or +5083560
            r'[+](\d{1,3}(?:,\d{3})*)',
            # 5,083,560 VND or 5083560 VND
            r'(\d{1,3}(?:,\d{3})*)\s*(?:VND|dong|đ)',
            # So tien GD:+195,051,040
            r'So\s+tien\s+GD:\s*[+]?(\d{1,3}(?:,\d{3})*)',
            # General number with commas
            r'(\d{1,3}(?:,\d{3})+)',
        ]

        self.company_patterns = [
            # CTY CO PHAN ACE MICRON
            r'(CTY\s+CO\s+PHAN\s+[A-Z\s]+?)(?:\s+thanh\s+toan|\s+TT|\s*$)',
            # CONG TY TNHH ISHISEI VIET NAM
            r'(CONG\s+TY\s+TNHH\s+[A-Z\s]+?)(?:\s+TT|\s+thanh\s+toan|\s*$)',
            # General company pattern
            r'((?:CTY|CONG\s+TY)(?:\s+(?:CO\s+PHAN|TNHH|CP))?\s+[A-Z\s]+?)(?:\s+(?:thanh\s+toan|TT|chuyen|nhan))',
            # Person names after "tu" (from) - capture full name including single letters
            r'tu\s+([A-Z]{1,}\s+[A-Z]{1,}(?:\s+[A-Z]{1,})*)',
            # Person names (all caps) with transfer keywords
            r'([A-Z]{2,}\s+[A-Z]{2,}(?:\s+[A-Z]{2,})*)\s+(?:chuyen|nhan)',
        ]

        self.reference_patterns = [
            # hoa don 5053
            r'(?:hoa\s+don|HD)\s*(\w+)',
            # BG0432906
            r'(BG\d+)',
            # Invoice patterns
            r'(?:theo|ref|invoice)\s*(\w+)',
            # General reference after specific keywords
            r'(?:so|ma|ref):\s*(\w+)',
        ]

        # Legacy bank patterns (kept for fallback)
        self.bank_patterns = [
            (r'MBBANK', BankType.MBBANK.value),
            (r'TECHCOMBANK', BankType.TECHCOMBANK.value),
            (r'VCB', BankType.VCB.value),
            (r'VIETCOMBANK', BankType.VIETCOMBANK.value),
            (r'BIDV', BankType.BIDV.value),
            (r'ACB', BankType.ACB.value),
            (r'VIETINBANK', BankType.VIETINBANK.value),
            (r'Shinhan', BankType.SHINHAN.value),
        ]

        # Enhanced bank-specific detection patterns
        self.bank_structure_patterns = {
            BankType.VIETINBANK.value: {
                'unique_identifiers': [
                    r'So\s+GD\s+goc:\s*\d+',  # "So GD goc: ********" - highly reliable
                    r'TT\s+[A-Z0-9]+\s+N\s+PREPMT',  # "TT VNMN2FF6Z N PREPMT"
                ],
                'datetime_patterns': [
                    r'\b\d{2}/\d{2}/\d{4}\b',  # DD/MM/YYYY anywhere in text
                    r'\b\d{2}:\d{2}:\d{2}\b',  # HH:MM:SS anywhere in text
                ],
                'account_patterns': [
                    r'\b\d{17}\b',  # 17-digit account numbers anywhere in text
                ],
                'confidence_weights': {
                    'unique_identifiers': 0.95,
                    'datetime_patterns': 0.85,
                    'account_patterns': 0.6,
                }
            },
            BankType.VIETCOMBANK.value: {
                'unique_identifiers': [
                    r'IBFPM\.\d+',  # "IBFPM.202507225087063779" - highly reliable
                ],
                'datetime_patterns': [
                    r'^\d{4}-\d{2}-\d{2}$',  # YYYY-MM-DD format on separate line (unique to Vietcombank)
                ],
                'transaction_patterns': [
                    r'\d{4}\s*-\s*\d+',  # "9920 - 00039"
                ],
                'currency_patterns': [
                    r'\d+,?\d*\s+VND',  # "3,960,000 VND"
                ],
                'confidence_weights': {
                    'unique_identifiers': 0.95,
                    'datetime_patterns': 0.85,
                    'transaction_patterns': 0.7,
                    'currency_patterns': 0.6,
                }
            },
            BankType.TECHCOMBANK.value: {
                'unique_identifiers': [
                    r'MBVCB\.\d+',  # "MBVCB.***********" - highly reliable
                ],
                'datetime_patterns': [
                    r'\d{2}/\d{2}/\d{4}\s+\d{2}:\d{2}:\d{2}',  # "25/07/2025 09:30:43" - combined date-time (unique to Techcombank)
                ],
                'reference_patterns': [
                    r'FT\d+',  # "FT25206435765859"
                ],
                'source_patterns': [
                    r'VIETCOMBANK\s+Hanoi\s+VNM',  # "VIETCOMBANK Hanoi VNM"
                ],
                'bank_mentions': [
                    r'TECHCOMBANK',
                    r'TECHC\s*OMBANK',  # "TECHC OMBANK" (with space)
                ],
                'confidence_weights': {
                    'unique_identifiers': 0.95,
                    'datetime_patterns': 0.85,
                    'reference_patterns': 0.7,
                    'source_patterns': 0.7,
                    'bank_mentions': 0.8,
                }
            }
        }

    def extract_amount(self, text: str) -> Tuple[Optional[int], float]:
        """
        Extract amount from SMS text
        Returns: (amount_in_vnd, confidence)
        """
        text_clean = text.upper().strip()

        for i, pattern in enumerate(self.amount_patterns):
            matches = re.findall(pattern, text_clean, re.IGNORECASE)
            if matches:
                try:
                    # Take the largest amount found (usually the transaction amount)
                    amounts = []
                    for match in matches:
                        # Remove commas and convert to int
                        amount_str = match.replace(',', '')
                        amount = int(amount_str)
                        amounts.append(amount)

                    if amounts:
                        # Return the largest amount (likely the transaction amount)
                        max_amount = max(amounts)
                        confidence = 0.9 if len(amounts) == 1 else 0.8
                        logger.debug(f"Amount extracted: {max_amount} (pattern {i+1}, confidence: {confidence})")
                        return max_amount, confidence
                        
                except ValueError:
                    logger.debug(f"ValueError converting amount match: {match}")
                    continue

        return None, 0.0
    
    def extract_company_name(self, text: str) -> Tuple[Optional[str], float]:
        """
        Extract company/sender name from SMS text
        Returns: (company_name, confidence)
        """
        text_clean = text.upper().strip()

        for i, pattern in enumerate(self.company_patterns):
            match = re.search(pattern, text_clean, re.IGNORECASE)
            if match:
                company = match.group(1).strip()
                # Clean up the company name
                company = re.sub(r'\s+', ' ', company)
                confidence = 0.9 if 'CTY' in company or 'CONG TY' in company else 0.7
                logger.debug(f"Sender extracted: '{company}' (pattern {i+1}, confidence: {confidence})")
                return company, confidence

        logger.debug(f"No sender found, using fallback: 'Cannot Detect'")
        return "Cannot Detect", 0.3
    
    def extract_reference(self, text: str) -> Tuple[Optional[str], float]:
        """
        Extract reference/invoice number from SMS text
        Returns: (reference, confidence)
        """
        text_clean = text.upper().strip()
        
        for pattern in self.reference_patterns:
            match = re.search(pattern, text_clean, re.IGNORECASE)
            if match:
                reference = match.group(1).strip()
                confidence = 0.8
                return reference, confidence
        
        return None, 0.0
    
    def detect_bank_by_structure(self, text: str) -> Tuple[Optional[str], float]:
        """
        Enhanced bank detection using structural patterns specific to each bank
        Returns: (bank, confidence)
        """
        text_clean = text.upper().strip()
        bank_scores = {}

        logger.info(f"=== BANK DETECTION DEBUG ===")
        logger.info(f"Input text length: {len(text_clean)} chars")
        logger.info(f"Text preview: {text_clean[:200]}...")

        # Check each bank's structural patterns
        for bank_name, patterns in self.bank_structure_patterns.items():
            logger.info(f"\n--- Testing {bank_name} ---")
            total_score = 0.0
            pattern_count = 0
            matched_patterns = []

            # Check each pattern type for this bank
            for pattern_type, pattern_list in patterns.items():
                if pattern_type == 'confidence_weights':
                    continue

                confidence_weight = patterns['confidence_weights'].get(pattern_type, 0.5)
                logger.info(f"  {pattern_type} (weight: {confidence_weight}):")

                pattern_matched = False
                for pattern in pattern_list:
                    match = re.search(pattern, text_clean, re.IGNORECASE)
                    if match:
                        total_score += confidence_weight
                        pattern_count += 1
                        matched_patterns.append(f"{pattern_type}: {pattern}")
                        logger.info(f"    ✅ MATCH: '{pattern}' -> '{match.group()}'")
                        pattern_matched = True
                        break  # Only count first match per pattern type
                    else:
                        logger.info(f"    ❌ NO MATCH: '{pattern}'")

                if not pattern_matched:
                    logger.info(f"    ⚠️  No patterns matched for {pattern_type}")

            # Calculate score for this bank
            if pattern_count > 0:
                num_pattern_types = len(patterns['confidence_weights'])
                raw_score = total_score / num_pattern_types
                boost_factor = 1 + pattern_count * 0.1
                final_score = min(raw_score * boost_factor, 1.0)

                bank_scores[bank_name] = final_score

                logger.info(f"  📊 SCORING for {bank_name}:")
                logger.info(f"    - Total score: {total_score:.2f}")
                logger.info(f"    - Pattern types: {num_pattern_types}")
                logger.info(f"    - Matched patterns: {pattern_count}")
                logger.info(f"    - Raw score: {raw_score:.3f} (total_score / pattern_types)")
                logger.info(f"    - Boost factor: {boost_factor:.2f} (1 + matches * 0.1)")
                logger.info(f"    - Final score: {final_score:.3f}")
                logger.info(f"    - Matched: {matched_patterns}")
            else:
                logger.info(f"  ❌ No patterns matched for {bank_name}")

        logger.info(f"\n=== FINAL RESULTS ===")
        logger.info(f"Bank scores: {bank_scores}")

        # Return bank with highest confidence
        if bank_scores:
            best_bank = max(bank_scores.items(), key=lambda x: x[1])
            logger.info(f"Best match: {best_bank[0]} with confidence {best_bank[1]:.3f}")
            return best_bank[0], best_bank[1]

        logger.info("No bank detected")
        return None, 0.0

    def extract_bank(self, text: str) -> Tuple[Optional[str], float]:
        """
        Extract bank identifier from SMS text using enhanced structural detection
        Falls back to simple name-based detection if structural detection fails
        Returns: (bank, confidence)
        """
        logger.info(f"\n🏦 === EXTRACT_BANK DEBUG ===")

        # First try enhanced structural detection
        bank, confidence = self.detect_bank_by_structure(text)
        logger.info(f"Structural detection result: bank='{bank}', confidence={confidence:.3f}")

        if bank and confidence > 0.3:  # Lowered threshold from 0.5 to 0.3
            logger.info(f"✅ Bank detected by structure: {bank} (confidence: {confidence:.3f}) - ABOVE THRESHOLD")
            return bank, confidence
        elif bank:
            logger.info(f"❌ Bank detected by structure: {bank} (confidence: {confidence:.3f}) - BELOW THRESHOLD (0.3)")
        else:
            logger.info(f"❌ No bank detected by structural patterns")

        # Fallback to simple name-based detection
        logger.info(f"🔄 Falling back to name-based detection...")
        text_clean = text.upper().strip()

        for pattern, bank_name in self.bank_patterns:
            if re.search(pattern, text_clean, re.IGNORECASE):
                logger.info(f"✅ Bank detected by name pattern: {bank_name} (confidence: 0.7)")
                return bank_name, 0.7

        logger.info(f"❌ No bank detected by any method")
        return None, 0.0
    
    # extract_transaction_type method removed - no longer needed

    def extract_all_info(self, sms_text: str) -> PaymentInfo:
        """
        Extract all available information from SMS text using regex
        Returns: PaymentInfo object with confidence score
        """
        if not sms_text or not sms_text.strip():
            return PaymentInfo(confidence=0.0)

        # Extract individual components
        amount, amount_conf = self.extract_amount(sms_text)
        sender, sender_conf = self.extract_company_name(sms_text)
        reference, ref_conf = self.extract_reference(sms_text)
        bank, bank_conf = self.extract_bank(sms_text)

        # Enhanced confidence calculation that prioritizes structural bank detection
        confidences = [amount_conf, sender_conf, ref_conf, bank_conf]
        non_zero_confidences = [c for c in confidences if c > 0]

        if non_zero_confidences:
            # Base confidence calculation
            overall_confidence = sum(non_zero_confidences) / len(confidences)
        else:
            overall_confidence = 0.0

        # Boost confidence if we have amount (most important)
        if amount is not None:
            overall_confidence = min(overall_confidence + 0.1, 1.0)

        # CRITICAL FIX: Boost confidence significantly if we have high-confidence structural bank detection
        # This prevents AI fallback from overriding correct bank detection
        if bank_conf > 0.8:  # High confidence structural bank detection
            logger.info(f"🏦 High-confidence bank detection ({bank_conf:.3f}) - boosting overall confidence")
            overall_confidence = max(overall_confidence, 0.75)  # Ensure we stay above AI fallback threshold

        # Additional boost for strong structural patterns (TECHCOMBANK, VIETCOMBANK, VIETINBANK)
        if bank and bank_conf > 0.5 and bank in ['TECHCOMBANK', 'VIETCOMBANK', 'VIETINBANK']:
            logger.info(f"🏦 Structural bank pattern detected ({bank}) - boosting confidence")
            overall_confidence = max(overall_confidence, 0.72)  # Ensure above 0.7 threshold

        return PaymentInfo(
            amount=amount,
            sender=sender,
            reference=reference,
            bank=bank,
            confidence=overall_confidence,
            raw_data={
                'amount_confidence': amount_conf,
                'sender_confidence': sender_conf,
                'reference_confidence': ref_conf,
                'bank_confidence': bank_conf,
                'structural_bank_boost': bank_conf > 0.8 or (bank_conf > 0.5 and bank in ['TECHCOMBANK', 'VIETCOMBANK', 'VIETINBANK'])
            }
        )
