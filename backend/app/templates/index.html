<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="referrer" content="no-referrer">
    <title>PTT Dashboard</title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Custom Tailwind Config -->
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#FFF7ED',
                            500: '#F15113',
                            600: '#D63F00',
                            700: '#B8340A',
                        },
                        glass: {
                            light: 'rgba(255, 255, 255, 0.1)',
                            medium: 'rgba(255, 255, 255, 0.2)',
                            dark: 'rgba(0, 0, 0, 0.1)',
                            border: 'rgba(255, 255, 255, 0.3)',
                        }
                    },
                    fontFamily: {
                        sans: ['Inter', 'system-ui', 'sans-serif'],
                        kanit: ['Kanit', 'sans-serif'],
                    },
                    backdropBlur: {
                        xs: '2px',
                        sm: '4px',
                        md: '8px',
                        lg: '12px',
                        xl: '16px',
                    }
                }
            }
        }
    </script>
    
    <!-- FontAwesome 6.4.0 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Kanit:wght@200;300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- React -->
    <script crossorigin src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script crossorigin src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>



    <!-- jQuery for backward compatibility -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    
    <style>
        body {
            font-family: 'Inter', sans-serif;
            font-weight: 400; /* Normal weight for body text */
        }
        
        h1, h2, h3, h4, h5, h6 {
            font-family: 'Kanit', sans-serif;
            font-weight: 400; /* Lighter weight for cleaner headings */
        }
        
        /* Override any bold text to be normal weight */
        .font-medium {
            font-weight: 500 !important;
        }
        
        .font-semibold {
            font-weight: 500 !important;
        }
        
        .font-bold {
            font-weight: 600 !important;
        }
        
        /* Table striping improvements */
        .table-striped tbody tr:nth-child(even) {
            background-color: #f8fafc !important; /* Very light gray */
        }
        
        .table-striped tbody tr:hover {
            background-color: #f1f5f9 !important; /* Slightly darker on hover */
        }
        
        /* Custom Tailwind configuration for PTT colors */
        .text-ptt-orange { color: #F15113; }
        .bg-ptt-orange { background-color: #F15113; }
        .border-ptt-orange { border-color: #F15113; }
        .ring-ptt-orange { --tw-ring-color: #F15113; }
        
        /* Loading overlay styles - Glassmorphic Design */
        #overlay {
            position: fixed;
            display: none;
            width: 100%;
            height: 100%;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(8px);
            -webkit-backdrop-filter: blur(8px);
            z-index: 9999;
        }
        
        #load {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            display: flex;
            align-items: center;
            justify-content: center;
            width: 64px;
            height: 64px;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
        }
        
        #load i {
            color: white;
            font-size: 18px;
        }
        
        .fa-spin {
            animation: fa-spin 1s linear infinite;
        }
        
        @keyframes fa-spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        /* Toast Animations */
        @keyframes slideInRight {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        @keyframes slideOutRight {
            from {
                transform: translateX(0);
                opacity: 1;
            }
            to {
                transform: translateX(100%);
                opacity: 0;
            }
        }

        .toast-enter {
            animation: slideInRight 0.3s ease-out;
        }

        .toast-exit {
            animation: slideOutRight 0.3s ease-in;
        }

        /* PTT Design System Colors - Glassmorphic */
        :root {
            --primary-50: #eff6ff;
            --primary-100: #dbeafe;
            --primary-200: #bfdbfe;
            --primary-300: #93c5fd;
            --primary-400: #60a5fa;
            --primary-500: #3b82f6;
            --primary-600: #2563eb;
            --primary-700: #1d4ed8;
            --primary-800: #1e40af;
            --primary-900: #1e3a8a;
            
            /* New Light Theme Glass System */
            --glass-light: rgba(255, 255, 255, 0.7);
            --glass-medium: rgba(255, 255, 255, 0.8);
            --glass-strong: rgba(255, 255, 255, 0.9);
            --glass-dark: rgba(0, 0, 0, 0.1);
            
            /* New Border System */
            --border-light: rgba(156, 163, 175, 0.5);
            
            /* New Text Colors */
            --text-primary: #1f2937;
            --text-secondary: #374151;
            --text-muted: #4b5563;
            --text-faint: #6b7280;
        }

        .bg-primary-500 { background-color: var(--primary-500); }
        .bg-primary-600 { background-color: var(--primary-600); }
        .text-primary-500 { color: var(--primary-500); }
        .border-primary-500 { border-color: var(--primary-500); }
        .hover\\:bg-primary-600:hover { background-color: var(--primary-600); }

        /* Custom scrollbar */
        ::-webkit-scrollbar {
            width: 6px;
        }

        ::-webkit-scrollbar-track {
            background: rgba(156, 163, 175, 0.1);
        }

        ::-webkit-scrollbar-thumb {
            background: rgba(156, 163, 175, 0.4);
            border-radius: 3px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: rgba(156, 163, 175, 0.6);
        }

        /* Glassmorphic hover animations */
        .glass-hover {
            transition: all 0.3s ease;
        }

        .glass-hover:hover {
            transform: translateY(-2px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        .glass-button-hover {
            transition: all 0.2s ease;
        }

        .glass-button-hover:hover {
            transform: scale(1.02);
        }

        /* Glass card animations */
        .glass-card-enter {
            animation: glassCardSlideIn 0.4s ease-out;
        }

        @keyframes glassCardSlideIn {
            from {
                opacity: 0;
                transform: translateY(20px) scale(0.95);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        /* Focus glow effect */
        .glass-focus:focus {
            box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.3);
        }

        /* Loading overlay improvements */
        .loading-overlay {
            backdrop-filter: blur(4px);
        }
        
        /* New Light Theme Glass Utilities */
        .glass-light {
            background: var(--glass-light);
            backdrop-filter: blur(12px);
            -webkit-backdrop-filter: blur(12px);
            border: 1px solid var(--border-light);
        }
        
        .glass-medium {
            background: var(--glass-medium);
            backdrop-filter: blur(12px);
            -webkit-backdrop-filter: blur(12px);
            border: 1px solid var(--border-light);
        }
        
        .glass-strong {
            background: var(--glass-strong);
            backdrop-filter: blur(12px);
            -webkit-backdrop-filter: blur(12px);
            border: 1px solid var(--border-light);
        }
        
        /* Theme Button Styles */
        .btn-primary {
            background-color: #2563eb;
            color: white;
            transition: all 0.2s ease;
        }
        
        .btn-primary:hover {
            background-color: #1d4ed8;
            box-shadow: 0 4px 12px rgba(37, 99, 235, 0.3);
        }
        
        .btn-secondary {
            background-color: #7c3aed;
            color: white;
            transition: all 0.2s ease;
        }
        
        .btn-secondary:hover {
            background-color: #6d28d9;
            box-shadow: 0 4px 12px rgba(124, 58, 237, 0.3);
        }
        
        /* Input Styles */
        .input-light {
            background: var(--glass-medium);
            color: var(--text-primary);
            border: 1px solid var(--border-light);
            transition: all 0.2s ease;
        }
        
        .input-light:focus {
            outline: none;
            border-color: transparent;
            box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.5);
        }
        
        .input-light::placeholder {
            color: var(--text-muted);
        }
    </style>
</head>
<body class="bg-gradient-to-br from-blue-100 via-purple-100 to-pink-100 h-screen">
    <!-- Loading Animation - PTT Approved Design -->
    <div id="overlay">
        <div id="load">
            <i class="fas fa-spinner fa-spin"></i>
        </div>
    </div>

    <!-- React App Root -->
    <div id="root"></div>

    <!-- Load Compiled PTT Components -->
    <script src="/static/scripts/ptt-components.js?v=8"></script>
    
    <!-- Initialize PTT Dashboard App -->
    <script>
        // Wait for all scripts to load
        window.addEventListener('DOMContentLoaded', function() {
            const { useState, useEffect } = React;
            const {
                Layout,
                Sidebar,
                Button,
                Card,
                Input,
                Textarea,
                FileUpload,
                Table,
                MisaCheck,
                PaymentManagement,
                UserProfile,
                ToastProvider,
                AuthProvider,
                AuthGuard,
                useAuth
            } = PTTComponents;
            
            function App() {
                // Restore state from localStorage or use defaults
                const [activePage, setActivePage] = useState(() => {
                    return localStorage.getItem('ptt-active-page') || 'dashboard';
                });
                const [sidebarVisible, setSidebarVisible] = useState(() => {
                    const saved = localStorage.getItem('ptt-sidebar-visible');
                    return saved !== null ? JSON.parse(saved) : true;
                });
                const { user, signOut, getIdToken } = useAuth();
                
                // Persist state changes to localStorage
                useEffect(() => {
                    localStorage.setItem('ptt-active-page', activePage);
                }, [activePage]);
                
                useEffect(() => {
                    localStorage.setItem('ptt-sidebar-visible', JSON.stringify(sidebarVisible));
                }, [sidebarVisible]);
                
                // Setup authenticated fetch for API calls
                useEffect(() => {
                    const originalFetch = window.fetch;
                    window.fetch = async function(url, options = {}) {
                        // Only add auth to our API endpoints
                        if (url.startsWith('/') && !url.includes('/static/')) {
                            try {
                                const token = await getIdToken();
                                if (token) {
                                    options.headers = {
                                        ...options.headers,
                                        'Authorization': `Bearer ${token}`
                                    };
                                }
                            } catch (error) {
                                console.warn('Failed to get auth token:', error);
                            }
                        }
                        
                        // Make the request
                        const response = await originalFetch.call(this, url, options);
                        
                        // Handle auth errors
                        if (response.status === 401 && url.startsWith('/')) {
                            console.warn('Authentication failed, token may be expired');
                            // Force sign out if token is invalid
                            try {
                                await signOut();
                            } catch (e) {
                                console.error('Error during forced sign out:', e);
                            }
                        }
                        
                        return response;
                    };
                    
                    // Cleanup function
                    return () => {
                        window.fetch = originalFetch;
                    };
                }, [getIdToken, signOut]);
                
                const sidebarItems = [
                    {
                        id: 'dashboard',
                        label: 'Dashboard',
                        icon: 'fas fa-chart-line',
                        disabled: true
                    },
                    {
                        id: 'misa-check',
                        label: 'Misa Check',
                        icon: 'fas fa-money-check-alt',
                        disabled: false,
                        active: activePage === 'misa-check'
                    },
                    {
                        id: 'payment-management',
                        label: 'Payment Management',
                        icon: 'fas fa-credit-card',
                        disabled: false,
                        active: activePage === 'payment-management'
                    },
                    {
                        id: 'user-profile',
                        label: 'Profile',
                        icon: 'fas fa-user-circle',
                        disabled: false,
                        active: activePage === 'user-profile'
                    },
                    {
                        id: 'settings',
                        label: 'Settings',
                        icon: 'fas fa-cog',
                        disabled: true
                    }
                ];
                
                const handleItemClick = (item) => {
                    if (!item.disabled) {
                        setActivePage(item.id);
                    }
                };
                
                const toggleSidebar = () => {
                    setSidebarVisible(!sidebarVisible);
                };
                
                const renderContent = () => {
                    switch(activePage) {
                        case 'misa-check':
                            return React.createElement(MisaCheck);
                        case 'payment-management':
                            return React.createElement(PaymentManagement);
                        case 'user-profile':
                            return React.createElement(UserProfile);
                        default:
                            return React.createElement(Card, {
                                title: 'Coming Soon',
                                className: 'p-3 m-3'
                            }, React.createElement('div', {
                                className: 'text-center py-8'
                            }, [
                                React.createElement('i', {
                                    key: 'icon',
                                    className: 'fas fa-tools text-4xl text-gray-300 mb-4'
                                }),
                                React.createElement('p', {
                                    key: 'text',
                                    className: 'text-gray-500'
                                }, 'This page is currently under development.')
                            ]));
                    }
                };
                
                // Enhanced header with demo styling and functionality
                const renderHeader = () => {
                    const getPageTitle = () => {
                        switch(activePage) {
                            case 'misa-check':
                                return 'Misa Check';
                            case 'payment-management':
                                return 'Payment Confirmation & Mapping';
                            case 'user-profile':
                                return 'Profile Settings';
                            case 'settings':
                                return 'Settings';
                            default:
                                return 'Dashboard';
                        }
                    };
                    
                    return React.createElement('div', {
                        className: 'px-6 py-3 flex items-center justify-between'
                    }, [
                        // Left side: Menu toggle + Title
                        React.createElement('div', {
                            key: 'left',
                            className: 'flex items-center gap-4'
                        }, [
                            React.createElement('button', {
                                key: 'menu-toggle',
                                onClick: toggleSidebar,
                                className: 'text-gray-700 hover:text-gray-900 transition-colors duration-200'
                            }, React.createElement('i', {
                                className: sidebarVisible ? 'fas fa-times w-5 h-5' : 'fas fa-bars w-5 h-5'
                            })),
                            React.createElement('h1', {
                                key: 'title',
                                className: 'text-lg font-semibold text-gray-800'
                            }, getPageTitle())
                        ]),
                        
                        // Right side: Actions and stats
                        React.createElement('div', {
                            key: 'right',
                            className: 'flex items-center gap-4'
                        }, [
                            // Action buttons for payment management
                            ...(activePage === 'payment-management' ? [
                                React.createElement('button', {
                                    key: 'refresh',
                                    className: 'p-2 bg-white/70 backdrop-blur-md border border-gray-200/50 rounded hover:bg-white/90 transition-colors duration-200',
                                    title: 'Refresh'
                                }, React.createElement('i', {
                                    className: 'fas fa-sync-alt w-4 h-4 text-gray-600'
                                })),
                                React.createElement('button', {
                                    key: 'export',
                                    className: 'p-2 bg-white/70 backdrop-blur-md border border-gray-200/50 rounded hover:bg-white/90 transition-colors duration-200',
                                    title: 'Export'
                                }, React.createElement('i', {
                                    className: 'fas fa-download w-4 h-4 text-gray-600'
                                })),
                                React.createElement('div', {
                                    key: 'divider',
                                    className: 'h-6 w-px bg-gray-200/50'
                                }),
                                React.createElement('span', {
                                    key: 'sync-status',
                                    className: 'text-gray-600 text-sm'
                                }, 'Real-time active'),
                                React.createElement('div', {
                                    key: 'stats',
                                    className: 'flex items-center gap-3'
                                }, [
                                    React.createElement('span', {
                                        key: 'payments',
                                        className: 'text-gray-800 text-sm'
                                    }, 'Today: 8 payments'),
                                    React.createElement('span', {
                                        key: 'amount',
                                        className: 'text-gray-800 font-bold text-sm'
                                    }, 'Amount: ₫142.5M')
                                ])
                            ] : []),
                            
                            // Chat/notification button
                            React.createElement('button', {
                                key: 'chat',
                                className: 'p-2 text-gray-700 hover:text-gray-900 transition-colors duration-200'
                            }, React.createElement('i', {
                                className: 'fas fa-comment-dots w-4 h-4'
                            }))
                        ])
                    ]);
                };
                
                // Custom sidebar with PTT logo and user info
                const renderSidebar = () => {
                    if (!sidebarVisible) return null;

                    return React.createElement(Sidebar, {
                        items: sidebarItems,
                        collapsed: false, // Always expanded when visible
                        onItemClick: handleItemClick,
                        onLogoClick: toggleSidebar,
                        user: {
                            name: user?.displayName || 'User',
                            email: user?.email || 'No email',
                            avatar: user?.photoURL
                        },
                        onLogout: signOut,
                        logo: React.createElement('img', {
                            src: '/static/phuthaitech_logo.svg',
                            alt: 'Phu Thai Technology Logo',
                            className: 'h-8 w-auto max-w-[200px] cursor-pointer'
                        })
                    });
                };
                
                return React.createElement(Layout, {
                    sidebar: renderSidebar(),
                    header: renderHeader(),
                    sidebarCollapsed: !sidebarVisible,
                    onOverlayClick: toggleSidebar
                }, renderContent());
            }
            
            // Render the app with authentication protection
            const root = ReactDOM.createRoot(document.getElementById('root'));
            
            // Import providers from new simplified structure
            const {
                RealTimeProvider
            } = PTTComponents;

            root.render(
                React.createElement(ToastProvider, null,
                    React.createElement(AuthProvider, null,
                        React.createElement(AuthGuard, null,
                            React.createElement(RealTimeProvider, null,
                                React.createElement(App)
                            )
                        )
                    )
                )
            );
            
            // Hide loading overlay once app is loaded
            $('#overlay').hide();
        });
    </script>
</body>
</html>