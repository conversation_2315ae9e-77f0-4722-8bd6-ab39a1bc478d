import React, { useState, useEffect, useRef, useMemo, useCallback } from 'react';
import { Button } from '../ui/button';
import { ChatInput } from '../ui/chat-input';
import { useAuth } from '../auth/AuthContext';
import { useToastHelpers } from '../ui/toast';
// Payment type imported via usePayments hook

// Non-real-time imports - simplified structure
import { usePaymentsAPI } from '../../hooks/usePaymentsAPI';
// Real-time only for core system status
import { useRealTime } from '../../realtime/core/RealTimeProvider';
// Firebase Realtime Database chat
import { useRealtimeChat } from '../../hooks/useRealtimeChat';

// TypeScript interfaces

// SMS Processing Types

interface PaymentData {
  id: number;
  customer: string;
  amount: number;
  status: string;
  metadata?: {
    reference?: string;
    bank?: string;
  };
}

interface ClassificationData {
  data?: {
    confidence?: number;
  };
}

const PaymentManagement: React.FC = () => {
  // Auth context
  const { user, getIdToken } = useAuth();
  const { success, error } = useToastHelpers();
  
  // Real-time contexts
  const { isConnected } = useRealTime();
  // Notification system removed for cleaner architecture

  // Firebase Realtime Database chat
  const {
    messages: chatMessages,
    loading: chatLoading,
    error: chatError,
    connected: chatConnected,
    sendMessage,
    clearChat
  } = useRealtimeChat({
    enabled: !!user,
    limit: 100
  });
  
  // Payment data from API (non-real-time)
  const {
    payments,
    filteredPayments,
    stats: paymentStats,
    loading: paymentsLoading,
    error: paymentsError,
    refresh: refreshPayments,
    searchTerm: paymentSearchTerm,
    setSearchTerm: setPaymentSearchTerm,
    statusFilter: paymentStatusFilter,
    setStatusFilter: setPaymentStatusFilter
  } = usePaymentsAPI();
  
  // State management
  const [smsInput, setSmsInput] = useState('');
  const [dateFilter, setDateFilter] = useState('all'); // Keep for future use
  const [searchMode, setSearchMode] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<string[]>([]);
  const [currentSearchIndex, setCurrentSearchIndex] = useState(0);

  // Refs
  const chatMessagesRef = useRef<HTMLDivElement>(null);
  const messageInputRef = useRef<HTMLTextAreaElement>(null);
  const searchInputRef = useRef<HTMLInputElement>(null);

  // Auto-scroll chat to bottom
  useEffect(() => {
    if (chatMessagesRef.current) {
      chatMessagesRef.current.scrollTop = chatMessagesRef.current.scrollHeight;
    }
  }, [chatMessages]);

  // Send SMS message through real-time chat system with SMS processing
  const sendSMSMessage = async () => {
    if (!smsInput.trim() || !user) return;

    const messageContent = smsInput.trim();
    setSmsInput('');

    try {
      // Send user message first
      const success = await sendMessage(messageContent, 'user');

      if (success) {
        // Process SMS in background
        await processSMSMessage(messageContent);
      } else {
        error('Message Failed', 'Failed to send message. Please try again.');
      }
    } catch (err) {
      console.error('Error sending message:', err);
      error('Message Failed', 'Failed to send message. Please try again.');
    }
  };

  // Process SMS message and send system response with reply context
  const processSMSMessage = async (messageContent: string) => {
    try {
      const token = await getIdToken();
      const response = await fetch('/api/v1/sms/process', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          message: messageContent,
          reporter: user?.email || 'unknown'
        })
      });

      const result = await response.json();

      if (result.success && result.is_bank_sms) {
        if (result.payment_created && result.payment) {
          // Success: Send system reply with payment details
          const systemReply = formatSuccessReply(result.payment, result.classification);
          await sendSystemReply(systemReply, messageContent, 'success');

          // Refresh payments to show new record
          await refreshPayments();
        } else {
          // SMS detected but payment creation failed
          const errorReply = formatErrorReply(result.error || 'Could not create payment record');
          await sendSystemReply(errorReply, messageContent, 'error');
        }
      } else if (result.is_bank_sms === false) {
        // Not a bank SMS - no action needed (normal chat flow)
        return;
      } else {
        // SMS detection/classification failed
        const errorReply = formatErrorReply(result.error || 'Could not process SMS');
        await sendSystemReply(errorReply, messageContent, 'error');
      }
    } catch (err) {
      console.error('Error processing SMS:', err);
      // Send system error reply
      const errorReply = formatErrorReply('System error processing SMS. Please try again.');
      await sendSystemReply(errorReply, messageContent, 'error');
    }
  };

  // Send system reply with context
  const sendSystemReply = async (replyContent: string, originalMessage: string, replyType: 'success' | 'error') => {
    const replyMessage = {
      text: replyContent,
      type: 'system',
      replyTo: {
        text: originalMessage.length > 100 ? originalMessage.substring(0, 100) + '...' : originalMessage,
        author: user?.displayName || user?.email || 'You'
      },
      replyType
    };

    // Send as system message with reply context
    await sendMessage(JSON.stringify(replyMessage), 'system');
  };

  // Format success reply for payment creation
  const formatSuccessReply = (payment: PaymentData, classification: ClassificationData): string => {
    const amount = payment.amount?.toLocaleString() || 'Unknown';
    const customer = payment.customer || 'Unknown';
    const reference = payment.metadata?.reference || 'N/A';
    const bank = payment.metadata?.bank || 'Unknown';
    const confidence = classification?.data?.confidence ? Math.round(classification.data.confidence * 100) : 0;

    return `Payment Record Created Successfully

💳 Payment ID: #${payment.id}
👤 Customer: ${customer}
💰 Amount: ${amount} VND
📄 Reference: ${reference}
🏦 Bank: ${bank}
🎯 Confidence: ${confidence}%
📊 Status: ${payment.status.charAt(0).toUpperCase() + payment.status.slice(1)}`;
  };

  // Format error reply for failed SMS processing with enhanced error details
  const formatErrorReply = (error: any): string => {
    // Handle both old string format and new structured error format
    if (typeof error === 'string') {
      return `SMS Processing Failed

❌ Error: ${error}

💡 Please check:
• SMS format is correct
• All required information is present
• Try again or contact support`;
    }

    // Handle structured error format
    if (error && typeof error === 'object') {
      let errorDetails = '';

      if (error.type === 'validation_error' && error.details?.missing_fields) {
        errorDetails = `\n📋 Missing fields: ${error.details.missing_fields.join(', ')}`;
        if (error.details.confidence !== undefined) {
          errorDetails += `\n🎯 Confidence: ${(error.details.confidence * 100).toFixed(1)}%`;
        }
      } else if (error.type === 'service_unavailable') {
        errorDetails = '\n🔧 AI service temporarily unavailable - using regex fallback';
      } else if (error.type === 'database_error') {
        errorDetails = '\n💾 Database connection issue - please try again';
      }

      return `SMS Processing Failed

❌ Error: ${error.message}${errorDetails}

💡 Please check:
• SMS format is correct
• All required information is present
• Try again or contact support`;
    }

    // Fallback for unknown error format
    return `SMS Processing Failed

❌ Error: Unknown error occurred

💡 Please try again or contact support`;
  };

  // Manual refresh for payments (for user-initiated refresh)
  const handleRefreshPayments = useCallback(() => {
    if (isConnected) {
      refreshPayments();
      success('Refreshing', 'Payment data is being refreshed.');
    } else {
      error('Connection Error', 'Unable to refresh. Please check your connection.');
    }
  }, [isConnected, refreshPayments, success, error]);

  // Clear chat through Firebase Realtime Database
  const handleClearChat = async () => {
    try {
      const success_result = await clearChat();
      if (success_result) {
        // Chat cleared successfully - no toast notification
        console.log('Chat cleared successfully');
      } else {
        error('Clear Failed', 'Failed to clear chat. Please try again.');
      }
    } catch (err) {
      error('Clear Failed', 'Failed to clear chat. Please try again.');
    }
  };

  // Search functionality
  const handleSearch = useCallback((query: string) => {
    if (!query.trim()) {
      setSearchResults([]);
      setCurrentSearchIndex(0);
      return;
    }
    
    const results: string[] = [];
    chatMessages.forEach(message => {
      if (message.text.toLowerCase().includes(query.toLowerCase())) {
        results.push(message.id.toString());
      }
    });
    
    setSearchResults(results);
    setCurrentSearchIndex(0);
  }, [chatMessages]);

  const navigateSearch = useCallback((direction: 'next' | 'prev') => {
    if (searchResults.length === 0) return;
    
    let newIndex: number;
    if (direction === 'next') {
      newIndex = (currentSearchIndex + 1) % searchResults.length;
    } else {
      newIndex = currentSearchIndex === 0 ? searchResults.length - 1 : currentSearchIndex - 1;
    }
    
    setCurrentSearchIndex(newIndex);
    
    // Scroll to the message
    const messageElement = document.getElementById(`message-${searchResults[newIndex]}`);
    if (messageElement) {
      messageElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
    }
  }, [searchResults, currentSearchIndex]);

  // Keyboard shortcuts
  const handleKeyDown = useCallback((e: KeyboardEvent) => {
    const chatContainer = chatMessagesRef.current;
    const messageInput = messageInputRef.current;
    
    if (!chatContainer || (!chatContainer.contains(document.activeElement) && document.activeElement !== messageInput)) {
      return;
    }
    
    if ((e.ctrlKey || e.metaKey) && e.key === 'f') {
      e.preventDefault();
      setSearchMode(true);
      setTimeout(() => {
        searchInputRef.current?.focus();
      }, 100);
    }
    
    if (e.key === 'Escape' && searchMode) {
      setSearchMode(false);
      setSearchQuery('');
      setSearchResults([]);
    }
  }, [searchMode]);

  useEffect(() => {
    document.addEventListener('keydown', handleKeyDown);
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [handleKeyDown]);

  useEffect(() => {
    if (searchMode) {
      handleSearch(searchQuery);
    }
  }, [searchQuery, searchMode, handleSearch]);

  // Format currency
  const formatCurrency = (amount: number, short = false): string => {
    if (short && amount >= 1000000) {
      return `₫${(amount / 1000000).toFixed(1)}M`;
    }
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND',
      minimumFractionDigits: 0
    }).format(amount);
  };

  // Format date for compact display
  const formatCompactDate = (isoString: string): string => {
    try {
      const date = new Date(isoString);
      const day = date.getDate().toString().padStart(2, '0');
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      const hours = date.getHours().toString().padStart(2, '0');
      const minutes = date.getMinutes().toString().padStart(2, '0');
      return `${day}/${month} ${hours}:${minutes}`;
    } catch (error) {
      return isoString; // Fallback to original if parsing fails
    }
  };

  // Format bank information for display
  const formatBankInfo = (bank?: string): string => {
    if (!bank) return '-';

    // Map bank codes to display names
    const bankDisplayNames: Record<string, string> = {
      'TECHCOMBANK': 'TCB',
      'VIETCOMBANK': 'VCB',
      'VIETINBANK': 'VTB',
      'MBBANK': 'MBB',
      'BIDV': 'BIDV',
      'ACB': 'ACB',
      'SHINHAN': 'SHB'
    };

    return bankDisplayNames[bank] || bank;
  };

  // Use statistics from payment API hook
  const stats = useMemo(() => {
    return {
      totalPayments: paymentStats.total,
      mappedPayments: paymentStats.mapped,
      unmappedPayments: paymentStats.unmapped,
      totalAmount: paymentStats.totalAmount
    };
  }, [paymentStats]);

  // Use filtered payments from the hook (no local filtering needed)
  const sortedPayments = filteredPayments;

  return (
    <div className="flex-1 p-4 grid grid-rows-[auto_1fr] gap-4 min-h-0 overflow-hidden">
      {/* Stats Cards Row */}
      <div className="flex gap-3 flex-shrink-0 overflow-x-auto">
          <div className="bg-white/80 backdrop-blur-md rounded-lg p-4 border border-gray-200/50 shadow-sm hover:shadow-md transition-all duration-200 flex-1 text-center min-w-[160px]">
            <p className="text-2xl font-bold text-gray-800 mb-1">{stats.totalPayments}</p>
            <p className="text-gray-600 text-sm">Total Payments</p>
          </div>
          
          <div className="bg-white/80 backdrop-blur-md rounded-lg p-4 border border-gray-200/50 shadow-sm hover:shadow-md transition-all duration-200 flex-1 text-center min-w-[160px]">
            <p className="text-2xl font-bold text-green-600 mb-1">{stats.mappedPayments}</p>
            <p className="text-gray-600 text-sm">Mapped</p>
          </div>
          
          <div className="bg-white/80 backdrop-blur-md rounded-lg p-4 border border-gray-200/50 shadow-sm hover:shadow-md transition-all duration-200 flex-1 text-center min-w-[160px]">
            <p className="text-2xl font-bold text-red-600 mb-1">{stats.unmappedPayments}</p>
            <p className="text-gray-600 text-sm">Unmapped</p>
          </div>
          
          <div className="bg-white/80 backdrop-blur-md rounded-lg p-4 border border-gray-200/50 shadow-sm hover:shadow-md transition-all duration-200 flex-1 text-center min-w-[160px]">
            <p className="text-2xl font-bold text-blue-600 mb-1">{formatCurrency(stats.totalAmount, true)}</p>
            <p className="text-gray-600 text-sm">Total Amount</p>
          </div>
        </div>

      {/* Main Content Area - Horizontal Layout */}
      <div className="flex-1 flex gap-4 min-h-0 overflow-hidden">
        {/* Left Panel: Chat - 35% */}
        <div className="w-[35%] flex flex-col min-h-0 overflow-hidden">
          <div className="bg-white/80 backdrop-blur-md rounded-xl border border-gray-200/50 shadow-lg flex flex-col h-full overflow-hidden">
            <div className="bg-white/90 backdrop-blur-md p-3 border-b border-gray-200/50 flex-shrink-0">
              <div className="flex items-center justify-between">
                <h3 className="text-base font-semibold text-gray-800">Payment Chat</h3>
                <div className="flex items-center gap-2">
                  <span className={`text-xs px-2 py-0.5 rounded-full ${
                    chatConnected
                      ? 'bg-green-100 text-green-700'
                      : 'bg-red-100 text-red-700'
                  }`}>
                    • {chatConnected ? 'Live' : 'Offline'}
                  </span>
                  {chatLoading && (
                    <span className="text-xs text-blue-600 bg-blue-100 px-2 py-0.5 rounded-full">
                      <i className="fas fa-sync fa-spin mr-1"></i>
                      Loading...
                    </span>
                  )}
                  {chatError && (
                    <span className="text-xs text-red-600 bg-red-100 px-2 py-0.5 rounded-full" title={chatError}>
                      <i className="fas fa-exclamation-triangle mr-1"></i>
                      Error
                    </span>
                  )}
                  <Button variant="secondary" size="sm" onClick={handleClearChat}>
                    <i className="fas fa-trash mr-1"></i>
                    Clear
                  </Button>
                </div>
              </div>
            </div>
            
            {/* Chat Messages */}
            <div 
              ref={chatMessagesRef}
              className="flex-1 overflow-y-auto px-4 py-6 min-h-0 scroll-smooth"
            >
              <div className="space-y-1">
                {chatMessages.map((message) => {
                  const isHighlighted = searchResults.includes(message.id.toString());
                  const isCurrent = isHighlighted && searchResults[currentSearchIndex] === message.id.toString();

                  // Parse system reply messages
                  let replyData = null;
                  let messageText = message.text;

                  if (message.type === 'system' && message.text.startsWith('{')) {
                    try {
                      replyData = JSON.parse(message.text);
                      messageText = replyData.text;
                    } catch (e) {
                      // If parsing fails, use original text
                      messageText = message.text;
                    }
                  }

                  // Check if this message is from the current user (but system messages are never treated as current user)
                  const isCurrentUser = message.uid === user?.uid && message.type !== 'system';

                  // Define color schemes for different message types
                  const getMessageStyles = () => {
                    if (isCurrentUser) {
                      return {
                        bubbleBg: 'bg-blue-500 text-white',
                        avatarBg: 'bg-blue-600 ring-2 ring-blue-400',
                        avatarIcon: 'text-white',
                        nameColor: 'text-blue-700',
                        timeColor: 'text-blue-600',
                        alignment: 'justify-end',
                        bubbleAlign: 'items-end',
                        orderClass: 'flex-row-reverse',
                        showAvatar: true,
                        maxWidth: 'max-w-[85%]'
                      };
                    }

                    switch (message.type) {
                      case 'admin':
                        return {
                          bubbleBg: 'bg-purple-100 text-purple-900 border border-purple-200',
                          avatarBg: 'bg-purple-600 ring-2 ring-purple-400',
                          avatarIcon: 'text-white',
                          nameColor: 'text-purple-700',
                          timeColor: 'text-purple-600',
                          alignment: 'justify-start',
                          bubbleAlign: 'items-start',
                          orderClass: '',
                          showAvatar: true,
                          maxWidth: 'max-w-[85%]'
                        };
                      case 'vip':
                        return {
                          bubbleBg: 'bg-amber-100 text-amber-900 border border-amber-200',
                          avatarBg: 'bg-gradient-to-br from-amber-500 to-yellow-600 ring-2 ring-amber-400',
                          avatarIcon: 'text-white',
                          nameColor: 'text-amber-700',
                          timeColor: 'text-amber-600',
                          alignment: 'justify-start',
                          bubbleAlign: 'items-start',
                          orderClass: '',
                          showAvatar: true,
                          maxWidth: 'max-w-[85%]'
                        };
                      case 'system':
                        if (replyData?.replyType === 'success') {
                          return {
                            bubbleBg: 'bg-green-50 text-green-900 border border-green-200',
                            avatarBg: 'bg-green-600',
                            avatarIcon: 'text-white',
                            nameColor: 'text-green-700',
                            timeColor: 'text-green-600',
                            alignment: 'justify-center',
                            bubbleAlign: 'items-center',
                            orderClass: '',
                            showAvatar: false,
                            maxWidth: 'max-w-full'
                          };
                        } else if (replyData?.replyType === 'error') {
                          return {
                            bubbleBg: 'bg-red-50 text-red-900 border border-red-200',
                            avatarBg: 'bg-red-600',
                            avatarIcon: 'text-white',
                            nameColor: 'text-red-700',
                            timeColor: 'text-red-600',
                            alignment: 'justify-center',
                            bubbleAlign: 'items-center',
                            orderClass: '',
                            showAvatar: false,
                            maxWidth: 'max-w-full'
                          };
                        }
                        return {
                          bubbleBg: 'bg-gray-100 text-gray-800 border border-gray-200',
                          avatarBg: 'bg-gray-600',
                          avatarIcon: 'text-white',
                          nameColor: 'text-gray-700',
                          timeColor: 'text-gray-600',
                          alignment: 'justify-center',
                          bubbleAlign: 'items-center',
                          orderClass: '',
                          showAvatar: false,
                          maxWidth: 'max-w-full'
                        };
                      default: // Regular user
                        return {
                          bubbleBg: 'bg-white text-gray-800 border border-gray-200',
                          avatarBg: 'bg-gray-400',
                          avatarIcon: 'text-white',
                          nameColor: 'text-gray-700',
                          timeColor: 'text-gray-500',
                          alignment: 'justify-start',
                          bubbleAlign: 'items-start',
                          orderClass: '',
                          showAvatar: true,
                          maxWidth: 'max-w-[85%]'
                        };
                    }
                  };

                  const styles = getMessageStyles();

                  return (
                    <div
                      key={message.id}
                      id={`message-${message.id}`}
                      className={`flex ${styles.alignment} mb-4 transition-all duration-300 ease-in-out hover:scale-[1.01] ${
                        isCurrent ? 'ring-2 ring-yellow-400 ring-offset-2 rounded-lg bg-yellow-50/20' :
                        isHighlighted ? 'ring-1 ring-yellow-300 ring-offset-1 rounded-lg bg-yellow-50/10' : ''
                      }`}
                    >
                      <div className={`flex ${styles.orderClass} ${styles.showAvatar ? 'gap-3' : 'gap-0'} ${styles.maxWidth} ${styles.bubbleAlign}`}>
                        {/* Avatar - only show if showAvatar is true */}
                        {styles.showAvatar && (
                          <div className={`w-10 h-10 rounded-full flex items-center justify-center flex-shrink-0 overflow-hidden shadow-lg ${styles.avatarBg} ring-offset-2`}>
                            {message.avatar ? (
                              <img
                                src={message.avatar}
                                alt={message.name || 'User'}
                                className="w-full h-full object-cover"
                              />
                            ) : (
                              <i className={`fas ${
                                message.type === 'user' ? 'fa-user' :
                                message.type === 'admin' ? 'fa-user-shield' :
                                message.type === 'vip' ? 'fa-star' :
                                'fa-robot'
                              } ${styles.avatarIcon} text-base`}></i>
                            )}
                          </div>
                        )}

                        {/* Message Bubble */}
                        <div className={`flex flex-col ${isCurrentUser ? 'items-end' : styles.showAvatar ? 'items-start' : 'items-center'} ${styles.showAvatar ? '' : 'w-full'}`}>
                          {/* Name and Time */}
                          <div className={`flex items-center gap-2 mb-1 px-1 ${isCurrentUser ? 'flex-row-reverse' : ''}`}>
                            <span className={`font-medium text-xs ${styles.nameColor}`}>
                              {message.type === 'user' ? message.name || 'Unknown User' :
                               message.type === 'admin' ? 'Admin' :
                               message.type === 'vip' ? 'VIP User' :
                               'System'}
                            </span>
                            <span className={`text-xs ${styles.timeColor}`}>
                              {message.createdAt ? new Date(message.createdAt).toLocaleTimeString() : 'Now'}
                            </span>
                            {replyData?.replyType && (
                              <span className={`text-xs px-2 py-0.5 rounded-full font-medium ${
                                replyData.replyType === 'success'
                                  ? 'bg-green-100 text-green-700'
                                  : 'bg-red-100 text-red-700'
                              }`}>
                                {replyData.replyType === 'success' ? 'Payment Created' : 'Processing Failed'}
                              </span>
                            )}
                          </div>

                          {/* Message Content Bubble */}
                          <div className={`rounded-2xl px-4 py-3 shadow-sm hover:shadow-md transition-shadow duration-200 ${styles.bubbleBg} ${
                            isCurrentUser ? 'rounded-br-sm' : 'rounded-bl-sm'
                          } max-w-full break-words`}>
                            {/* Reply Context */}
                            {replyData?.replyTo && (
                              <div className={`mb-2 p-2 rounded-lg border-l-3 ${
                                replyData.replyType === 'success'
                                  ? 'bg-green-100/50 border-green-400'
                                  : 'bg-red-100/50 border-red-400'
                              }`}>
                                <div className="text-xs opacity-75 mb-1">
                                  <i className="fas fa-reply mr-1"></i>
                                  Replying to {replyData.replyTo.author}
                                </div>
                                <div className="text-xs italic opacity-90">
                                  "{replyData.replyTo.text}"
                                </div>
                              </div>
                            )}

                            {/* Message Text */}
                            <pre className={`whitespace-pre-wrap font-sans text-sm ${
                              isCurrentUser ? 'text-white' : ''
                            }`}>{messageText}</pre>
                          </div>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
            
            {/* Search Bar */}
            {searchMode && (
              <div className="absolute top-12 right-4 bg-white/95 backdrop-blur-sm border border-gray-200 rounded-lg shadow-lg p-3 z-10">
                <div className="flex items-center gap-2 mb-2">
                  <i className="fas fa-search text-gray-500 text-sm"></i>
                  <input
                    ref={searchInputRef}
                    type="text"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    placeholder="Search messages..."
                    className="bg-white/80 text-gray-800 placeholder-gray-500 border border-gray-200 focus:ring-2 focus:ring-blue-400/50 focus:border-transparent rounded px-2 py-1 text-sm w-48"
                    onKeyDown={(e) => {
                      if (e.key === 'Enter') {
                        navigateSearch('next');
                      } else if (e.key === 'Escape') {
                        setSearchMode(false);
                        setSearchQuery('');
                        setSearchResults([]);
                      }
                    }}
                  />
                  <button
                    onClick={() => {
                      setSearchMode(false);
                      setSearchQuery('');
                      setSearchResults([]);
                    }}
                    className="text-gray-500 hover:text-gray-700 p-1"
                  >
                    <i className="fas fa-times text-sm"></i>
                  </button>
                </div>
                {searchResults.length > 0 && (
                  <div className="flex items-center gap-2 text-xs text-gray-600">
                    <span>{currentSearchIndex + 1} of {searchResults.length}</span>
                    <div className="flex gap-1">
                      <button
                        onClick={() => navigateSearch('prev')}
                        className="p-1 hover:bg-gray-100 rounded"
                        disabled={searchResults.length === 0}
                      >
                        <i className="fas fa-chevron-up"></i>
                      </button>
                      <button
                        onClick={() => navigateSearch('next')}
                        className="p-1 hover:bg-gray-100 rounded"
                        disabled={searchResults.length === 0}
                      >
                        <i className="fas fa-chevron-down"></i>
                      </button>
                    </div>
                  </div>
                )}
              </div>
            )}
            
            {/* Message Input Area */}
            <div className="p-3 border-t border-gray-200/50 flex-shrink-0">
              <ChatInput
                value={smsInput}
                onChange={setSmsInput}
                onSend={sendSMSMessage}
                placeholder="Type your message here or paste bank SMS notification..."
                disabled={!smsInput.trim() || !chatConnected}
                rows={3}
                maxRows={6}
              />
            </div>
          </div>
        </div>

        {/* Right Panel: Table - 65% */}
        <div className="flex-1 flex flex-col min-h-0 overflow-hidden">
          <div className="bg-white/80 backdrop-blur-md rounded-xl border border-gray-200/50 shadow-lg flex flex-col h-full overflow-hidden">
            {/* Table Header */}
            <div className="bg-white/90 backdrop-blur-md p-3 border-b border-gray-200/50 flex-shrink-0">
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2">
                <div className="flex items-center gap-2">
                  <h3 className="text-sm font-semibold text-gray-800">Payment Records</h3>
                  <span className="text-xs text-gray-600 bg-gray-100 px-2 py-0.5 rounded-full">
                    {payments.length} records
                  </span>
                  {paymentsLoading && (
                    <span className="text-xs text-blue-600 bg-blue-100 px-2 py-0.5 rounded-full">
                      <i className="fas fa-sync fa-spin mr-1"></i>
                      Syncing...
                    </span>
                  )}
                  {paymentsError && (
                    <span className="text-xs text-red-600 bg-red-100 px-2 py-0.5 rounded-full">
                      <i className="fas fa-exclamation-triangle mr-1"></i>
                      Error
                    </span>
                  )}
                </div>
                <div className="flex items-center gap-2 flex-wrap">
                  <Button variant="secondary" size="sm" onClick={handleRefreshPayments} disabled={!isConnected}>
                    <i className="fas fa-sync mr-1"></i>
                    Refresh
                  </Button>
                  <div className="relative">
                    <i className="fas fa-search absolute left-3 top-1/2 -translate-y-1/2 w-4 h-4 text-gray-600"></i>
                    <input
                      type="text"
                      placeholder="Search..."
                      value={paymentSearchTerm}
                      onChange={(e) => setPaymentSearchTerm(e.target.value)}
                      className="bg-white/80 text-gray-800 placeholder-gray-500 border border-gray-200/50 focus:ring-2 focus:ring-blue-400/50 focus:border-transparent pl-9 pr-3 py-1.5 rounded text-sm w-32 sm:w-40 lg:w-48"
                    />
                  </div>
                  <select 
                    className="bg-white/80 text-gray-800 border border-gray-200/50 focus:ring-2 focus:ring-blue-400/50 focus:border-transparent px-3 py-1.5 rounded text-sm"
                    value={paymentStatusFilter}
                    onChange={(e) => setPaymentStatusFilter(e.target.value as 'all' | 'mapped' | 'unmapped')}
                  >
                    <option value="all">All Status</option>
                    <option value="mapped">Mapped</option>
                    <option value="unmapped">Unmapped</option>
                  </select>
                  <select 
                    className="bg-white/80 text-gray-800 border border-gray-200/50 focus:ring-2 focus:ring-blue-400/50 focus:border-transparent px-3 py-1.5 rounded text-sm hidden sm:inline-block"
                    value={dateFilter}
                    onChange={(e) => setDateFilter(e.target.value)}
                  >
                    <option value="all">All Dates</option>
                    <option value="today">Today</option>
                    <option value="week">This Week</option>
                    <option value="month">This Month</option>
                  </select>
                </div>
              </div>
            </div>

            {/* Table Content */}
            <div className="flex-1 overflow-y-auto bg-white/50 min-h-0 overflow-x-auto">
              <table className="w-full min-w-[900px]">
                <thead className="sticky top-0 bg-white/90 backdrop-blur-md border-b border-gray-200/50 shadow-sm">
                  <tr>
                    <th className="text-left py-2 px-2 text-gray-700 font-medium text-xs">ID</th>
                    <th className="text-left py-2 px-2 text-gray-700 font-medium text-xs">Time</th>
                    <th className="text-left py-2 px-2 text-gray-700 font-medium text-xs">Customer</th>
                    <th className="text-left py-2 px-2 text-gray-700 font-medium text-xs">Amount</th>
                    <th className="text-left py-2 px-2 text-gray-700 font-medium text-xs">Bank</th>
                    <th className="text-left py-2 px-2 text-gray-700 font-medium text-xs">Invoice/Ref</th>
                    <th className="text-left py-2 px-2 text-gray-700 font-medium text-xs">Status</th>
                    <th className="text-left py-2 px-2 text-gray-700 font-medium text-xs">Reporter</th>
                    <th className="text-left py-2 px-2 text-gray-700 font-medium text-xs">Confirmed By</th>
                    <th className="text-center py-2 px-2 text-gray-700 font-medium text-xs">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {(() => {
                    // Use filtered payments from the hook (no local filtering needed)
                    if (sortedPayments.length === 0) {
                      return (
                        <tr>
                          <td colSpan={10} className="py-8 px-3 text-center">
                            <div className="flex flex-col items-center gap-2">
                              <i className="fas fa-search text-4xl text-gray-300"></i>
                              <p className="text-gray-600">No payments found{paymentSearchTerm ? ` matching "${paymentSearchTerm}"` : ''}</p>
                              {paymentSearchTerm && (
                                <button
                                  onClick={() => setPaymentSearchTerm('')}
                                  className="text-sm text-blue-600 hover:text-blue-700"
                                >
                                  Clear search
                                </button>
                              )}
                            </div>
                          </td>
                        </tr>
                      );
                    }

                    return sortedPayments.map((payment, index) => (
                      <tr 
                        key={payment.id} 
                        className={`border-b border-gray-200/50 ${
                          index % 2 === 0 ? 'bg-gray-50/50' : 'bg-white/50'
                        } hover:bg-blue-50/50 transition-colors duration-150`}
                      >
                        <td className="py-2 px-2 text-gray-800 text-xs font-medium">#{payment.id}</td>
                        <td className="py-2 px-2 text-gray-700 text-xs">{formatCompactDate(payment.metadata.created_at)}</td>
                        <td className="py-2 px-2 text-gray-800 text-xs font-medium">{payment.customer}</td>
                        <td className="py-2 px-2 text-gray-800 font-semibold text-xs">{formatCurrency(payment.amount)}</td>
                        <td className="py-2 px-2 text-gray-700 text-xs">
                          <div className="flex items-center gap-1">
                            <i className="fas fa-university text-gray-400 text-xs"></i>
                            <span>{formatBankInfo(payment.metadata?.bank)}</span>
                          </div>
                        </td>
                        <td className="py-2 px-2 text-gray-700 text-xs">
                          {payment.invoice || payment.metadata?.reference || '-'}
                        </td>
                        <td className="py-2 px-2">
                          <span className={`px-1.5 py-0.5 rounded-full text-xs font-medium border ${
                            payment.status === 'mapped'
                              ? 'bg-green-500/20 text-green-700 border-green-500/30'
                              : 'bg-red-500/20 text-red-700 border-red-500/30'
                          }`}>
                            {payment.status.charAt(0).toUpperCase() + payment.status.slice(1)}
                          </span>
                        </td>
                        <td className="py-2 px-2 text-gray-700 text-xs">{payment.reporter}</td>
                        <td className="py-2 px-2 text-gray-700 text-xs">{payment.confirmed_by || '-'}</td>
                        <td className="py-2 px-2">
                          <div className="flex items-center justify-center gap-1">
                            <button
                              className="p-1 text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded transition-all duration-200"
                              title="View Details"
                            >
                              <i className="fas fa-eye w-3 h-3"></i>
                            </button>
                            <button
                              className="p-1 text-gray-600 hover:text-purple-600 hover:bg-purple-50 rounded transition-all duration-200"
                              title="Edit Record"
                            >
                              <i className="fas fa-edit w-3 h-3"></i>
                            </button>
                            <button
                              className="p-1 text-gray-600 hover:text-red-600 hover:bg-red-50 rounded transition-all duration-200"
                              title="Delete Record"
                            >
                              <i className="fas fa-trash w-3 h-3"></i>
                            </button>
                          </div>
                        </td>
                      </tr>
                    ));
                  })()} 
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PaymentManagement;