import React from 'react'

export interface LayoutProps extends React.HTMLAttributes<HTMLDivElement> {
  /**
   * Sidebar component (optional)
   */
  sidebar?: React.ReactNode
  /**
   * Header component (optional)
   */
  header?: React.ReactNode
  /**
   * Main content area
   */
  children: React.ReactNode
  /**
   * Whether sidebar is collapsed
   */
  sidebarCollapsed?: boolean
  /**
   * Callback when mobile overlay is clicked (to close sidebar)
   */
  onOverlayClick?: () => void
  /**
   * Additional CSS classes
   */
  className?: string
}

/**
 * Layout Component
 * 
 * A flexible layout wrapper with glassmorphic design.
 * Provides responsive layout structure with optional sidebar and header.
 * Features glassmorphic styling with backdrop blur effects and transparent backgrounds.
 * 
 * @example
 * ```tsx
 * <Layout
 *   sidebar={<Sidebar />}
 *   header={<Header />}
 *   sidebarCollapsed={false}
 *   onOverlayClick={() => setSidebarVisible(false)}
 * >
 *   <Card title="Dashboard Content">
 *     <p>Your main content here</p>
 *   </Card>
 * </Layout>
 *
 * // Simple layout without sidebar
 * <Layout header={<Header />}>
 *   <div>Main content without sidebar</div>
 * </Layout>
 * ```
 */
export function Layout({
  sidebar,
  header,
  children,
  sidebarCollapsed = false,
  onOverlayClick,
  className = '',
  ...props
}: LayoutProps) {
  
  const layoutClasses = [
    'h-screen flex flex-col overflow-hidden',
    className
  ].filter(Boolean).join(' ')

  const mainClasses = [
    'flex flex-1 transition-all duration-300 overflow-hidden',
    sidebar && 'relative'
  ].filter(Boolean).join(' ')

  const contentClasses = [
    'flex-1 flex flex-col transition-all duration-300 overflow-hidden',
    sidebar && !sidebarCollapsed && 'ml-64',
    sidebar && sidebarCollapsed && 'ml-16'
  ].filter(Boolean).join(' ')

  const sidebarClasses = [
    'fixed left-0 top-0 h-full z-30 transition-all duration-300',
    sidebarCollapsed ? 'w-16' : 'w-64',
    'bg-white/90 backdrop-blur-md border-r border-gray-200/50 shadow-xl'
  ].filter(Boolean).join(' ')

  return (
    <div className={layoutClasses} {...props}>
      {/* Sidebar */}
      {sidebar && (
        <div className={sidebarClasses}>
          {sidebar}
        </div>
      )}

      {/* Main Content Area */}
      <div className={mainClasses}>
        <div className={contentClasses}>
          {/* Header */}
          {header && (
            <header className="bg-white/80 backdrop-blur-md border-b border-gray-200/50 shadow-sm">
              {header}
            </header>
          )}

          {/* Main Content */}
          <main className="flex-1 min-h-0 flex flex-col overflow-hidden">
            {children}
          </main>
        </div>
      </div>

      {/* Mobile Sidebar Overlay */}
      {sidebar && !sidebarCollapsed && (
        <div
          className="fixed inset-0 bg-black/30 backdrop-blur-sm z-20 lg:hidden cursor-pointer"
          onClick={onOverlayClick}
          role="button"
          tabIndex={0}
          onKeyDown={(e) => {
            if ((e.key === 'Enter' || e.key === ' ') && onOverlayClick) {
              e.preventDefault()
              onOverlayClick()
            }
          }}
          aria-label="Close sidebar"
        />
      )}
    </div>
  )
}

export default Layout