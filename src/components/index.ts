// PTT Component Library
// Restructured with clean separation between UI components and features

// ✅ UI COMPONENTS (Design System)
export {
  Button,
  Card,
  Input,
  Textarea,
  ChatInput,
  Table,
  FileUpload,
  Layout,
  Sidebar,
  ToastProvider,
  useToast,
  useToastHelpers,
  type ButtonProps
} from './ui'

// ✅ AUTHENTICATION COMPONENTS
export {
  AuthProvider,
  useAuth,
  LoginScreen,
  AuthGuard,
  type AuthUser
} from './auth'

// ✅ FEATURE COMPONENTS
export { MisaCheck } from './misa-check'
export { default as PaymentManagement } from './payment-management/PaymentManagement'
export { UserProfile } from './user-profile'

// ✅ REAL-TIME SYSTEM PROVIDERS
export { RealTimeProvider } from '../realtime/core/RealTimeProvider'

// Clean exports only - PTT* legacy exports removed
// All components now use standard names for better maintainability