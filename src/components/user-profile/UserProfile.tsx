import React, { useState, useEffect, useCallback } from 'react';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Card } from '../ui/card';
import { FileUpload } from '../ui/file-upload';
import { useAuth } from '../auth/AuthContext';
import { useToastHelpers } from '../ui/toast';
import { useUserProfile } from '../../hooks/useUserProfile';
import { validateNickname, validateImageFile } from '../../utils/validation';
import { createImagePreview, cleanupImagePreview, formatFileSize } from '../../utils/imageUpload';
import type { UserProfileFormData, UserProfileFormErrors } from '../../types/user';

const UserProfile: React.FC = () => {
  const { user } = useAuth();
  const { success, error } = useToastHelpers();
  
  // Profile data and operations
  const {
    profile,
    loading,
    saving,
    uploading,
    error: profileError,
    updateProfile,
    uploadAvatar,
    refresh
  } = useUserProfile();

  // Form state
  const [formData, setFormData] = useState<UserProfileFormData>({
    nickname: '',
    avatarFile: null,
    avatarPreview: null
  });

  const [errors, setErrors] = useState<UserProfileFormErrors>({});
  const [hasChanges, setHasChanges] = useState(false);

  // Initialize form data when profile loads
  useEffect(() => {
    if (profile) {
      setFormData(prev => ({
        ...prev,
        nickname: profile.display_name || ''
      }));
    }
  }, [profile]);

  // Cleanup preview URLs on unmount
  useEffect(() => {
    return () => {
      if (formData.avatarPreview) {
        cleanupImagePreview(formData.avatarPreview);
      }
    };
  }, [formData.avatarPreview]);

  // Handle nickname change
  const handleNicknameChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setFormData(prev => ({ ...prev, nickname: value }));
    setHasChanges(true);
    
    // Clear nickname error when user starts typing
    if (errors.nickname) {
      setErrors(prev => ({ ...prev, nickname: undefined }));
    }
  }, [errors.nickname]);

  // Handle avatar file selection
  const handleAvatarSelect = useCallback((files: FileList) => {
    const file = files[0];
    if (!file) return;

    // Validate file
    const validation = validateImageFile(file);
    if (!validation.valid) {
      setErrors(prev => ({ ...prev, avatar: validation.error }));
      return;
    }

    // Clear previous preview
    if (formData.avatarPreview) {
      cleanupImagePreview(formData.avatarPreview);
    }

    // Create new preview
    const previewUrl = createImagePreview(file);
    setFormData(prev => ({
      ...prev,
      avatarFile: file,
      avatarPreview: previewUrl
    }));
    setHasChanges(true);
    
    // Clear avatar error
    if (errors.avatar) {
      setErrors(prev => ({ ...prev, avatar: undefined }));
    }
  }, [formData.avatarPreview, errors.avatar]);

  // Validate form
  const validateForm = useCallback((): boolean => {
    const newErrors: UserProfileFormErrors = {};

    // Validate nickname
    if (formData.nickname.trim()) {
      if (!validateNickname(formData.nickname.trim())) {
        newErrors.nickname = 'Nickname must be 2-50 characters and contain only letters, numbers, spaces, hyphens, and underscores';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  }, [formData.nickname]);

  // Handle form submission
  const handleSave = useCallback(async () => {
    if (!validateForm()) {
      return;
    }

    try {
      let avatarUrl = profile?.photo_url;

      // Upload avatar if a new file is selected
      if (formData.avatarFile) {
        const uploadedUrl = await uploadAvatar(formData.avatarFile);
        if (!uploadedUrl) {
          error('Upload Failed', 'Failed to upload avatar image');
          return;
        }
        avatarUrl = uploadedUrl;
      }

      // Update profile
      const updateData: any = {};
      if (formData.nickname.trim() !== (profile?.display_name || '')) {
        updateData.display_name = formData.nickname.trim();
      }
      if (avatarUrl !== profile?.photo_url) {
        updateData.photo_url = avatarUrl;
      }

      if (Object.keys(updateData).length > 0) {
        const updateSuccess = await updateProfile(updateData);
        if (updateSuccess) {
          success('Profile Updated', 'Your profile has been updated successfully');
          setHasChanges(false);
          
          // Clear avatar file and preview
          if (formData.avatarPreview) {
            cleanupImagePreview(formData.avatarPreview);
          }
          setFormData(prev => ({
            ...prev,
            avatarFile: null,
            avatarPreview: null
          }));
        } else {
          error('Update Failed', 'Failed to update profile');
        }
      } else {
        setHasChanges(false);
      }

    } catch (err) {
      console.error('Error saving profile:', err);
      error('Save Failed', 'An unexpected error occurred');
    }
  }, [validateForm, formData, profile, uploadAvatar, updateProfile, success, error]);

  // Handle cancel
  const handleCancel = useCallback(() => {
    // Reset form to original values
    setFormData({
      nickname: profile?.display_name || '',
      avatarFile: null,
      avatarPreview: null
    });
    setErrors({});
    setHasChanges(false);
    
    // Cleanup preview
    if (formData.avatarPreview) {
      cleanupImagePreview(formData.avatarPreview);
    }
  }, [profile, formData.avatarPreview]);

  // Format member since date
  const formatMemberSince = (dateString: string): string => {
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString('en-US', { 
        year: 'numeric', 
        month: 'long' 
      });
    } catch {
      return 'Unknown';
    }
  };

  // Get current avatar URL (preview or existing)
  const getCurrentAvatarUrl = (): string | null => {
    if (formData.avatarPreview) {
      return formData.avatarPreview;
    }
    return profile?.photo_url || null;
  };

  if (loading) {
    return (
      <div className="flex-1 p-4 flex items-center justify-center">
        <div className="text-center">
          <i className="fas fa-spinner fa-spin text-2xl text-gray-400 mb-2"></i>
          <p className="text-gray-600">Loading profile...</p>
        </div>
      </div>
    );
  }

  if (profileError) {
    return (
      <div className="flex-1 p-4 flex items-center justify-center">
        <div className="text-center">
          <i className="fas fa-exclamation-triangle text-2xl text-red-400 mb-2"></i>
          <p className="text-red-600 mb-4">{profileError}</p>
          <Button onClick={refresh} variant="primary">
            Try Again
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="flex-1 p-4 space-y-4 overflow-y-auto">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-800">Profile Settings</h1>
          <p className="text-gray-600">Manage your account information and preferences</p>
        </div>
      </div>

      {/* Main Content */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Avatar Card */}
        <div className="lg:col-span-1">
          <Card title="Profile Picture" className="h-fit">
            <div className="space-y-4">
              {/* Current Avatar */}
              <div className="flex justify-center">
                <div className="relative">
                  {getCurrentAvatarUrl() ? (
                    <img
                      src={getCurrentAvatarUrl()!}
                      alt="Profile Avatar"
                      className="w-32 h-32 rounded-full object-cover border-4 border-white shadow-lg"
                    />
                  ) : (
                    <div className="w-32 h-32 rounded-full bg-gray-200 border-4 border-white shadow-lg flex items-center justify-center">
                      <i className="fas fa-user text-4xl text-gray-400"></i>
                    </div>
                  )}
                  {formData.avatarPreview && (
                    <div className="absolute -top-2 -right-2 bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs">
                      <i className="fas fa-check"></i>
                    </div>
                  )}
                </div>
              </div>

              {/* Upload Section */}
              <div className="space-y-2">
                <FileUpload
                  accept="image/*"
                  onFileSelect={handleAvatarSelect}
                  variant="compact"
                  disabled={uploading}
                  loading={uploading}
                  maxSize={5 * 1024 * 1024} // 5MB
                />
                
                {formData.avatarFile && (
                  <div className="text-sm text-gray-600 text-center">
                    Selected: {formData.avatarFile.name} ({formatFileSize(formData.avatarFile.size)})
                  </div>
                )}
                
                {errors.avatar && (
                  <p className="text-sm text-red-600 text-center">{errors.avatar}</p>
                )}
              </div>

              <div className="text-xs text-gray-500 text-center">
                Supported formats: JPG, PNG, WebP<br />
                Maximum size: 5MB
              </div>
            </div>
          </Card>
        </div>

        {/* Profile Form */}
        <div className="lg:col-span-2">
          <Card title="Profile Information">
            <div className="space-y-6">
              {/* Nickname */}
              <div>
                <Input
                  label="Display Name"
                  type="text"
                  value={formData.nickname}
                  onChange={handleNicknameChange}
                  placeholder="Enter your display name"
                  error={errors.nickname}
                  helperText="This name will be displayed throughout the platform"
                  icon="fas fa-user"
                />
              </div>

              {/* Email (Read-only) */}
              <div>
                <Input
                  label="Email Address"
                  type="email"
                  value={user?.email || ''}
                  disabled
                  icon="fas fa-envelope"
                  helperText="Email cannot be changed"
                />
              </div>

              {/* Member Since */}
              <div>
                <Input
                  label="Member Since"
                  type="text"
                  value={profile?.created_at ? formatMemberSince(profile.created_at) : 'Unknown'}
                  disabled
                  icon="fas fa-calendar"
                />
              </div>

              {/* Action Buttons */}
              <div className="flex gap-3 pt-4">
                <Button
                  onClick={handleSave}
                  variant="primary"
                  loading={saving || uploading}
                  disabled={!hasChanges || saving || uploading}
                  icon="fas fa-save"
                >
                  {saving || uploading ? 'Saving...' : 'Save Changes'}
                </Button>
                
                <Button
                  onClick={handleCancel}
                  variant="secondary"
                  disabled={!hasChanges || saving || uploading}
                >
                  Cancel
                </Button>
              </div>

              {errors.general && (
                <div className="p-3 bg-red-50 border border-red-200 rounded-md">
                  <p className="text-sm text-red-600">{errors.general}</p>
                </div>
              )}
            </div>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default UserProfile;
