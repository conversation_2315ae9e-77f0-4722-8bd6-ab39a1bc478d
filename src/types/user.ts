/**
 * User Profile Types
 * Type definitions for user profile data and operations
 */

export interface UserProfile {
  uid: string;
  email: string | null;
  display_name: string | null;
  photo_url: string | null;
  email_verified: boolean;
  created_at: string;
  updated_at: string;
  last_login: string;
  is_active: boolean;
  role: string;
}

export interface UserProfileUpdateData {
  display_name?: string;
  photo_url?: string;
}

export interface UserProfileFormData {
  nickname: string;
  avatarFile: File | null;
  avatarPreview: string | null;
}

export interface UserProfileFormErrors {
  nickname?: string;
  avatar?: string;
  general?: string;
}

export interface UserProfileState {
  profile: UserProfile | null;
  loading: boolean;
  saving: boolean;
  uploading: boolean;
  error: string | null;
  lastUpdated: Date | null;
}

export interface UseUserProfileResult {
  // Data
  profile: UserProfile | null;
  
  // Loading states
  loading: boolean;
  saving: boolean;
  uploading: boolean;
  
  // Error state
  error: string | null;
  
  // Actions
  updateProfile: (data: UserProfileUpdateData) => Promise<boolean>;
  uploadAvatar: (file: File) => Promise<string | null>;
  refresh: () => Promise<void>;
  
  // Metadata
  lastUpdated: Date | null;
}
