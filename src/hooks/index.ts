// Real-time Hooks Index
// Custom hooks for real-time functionality and state management

// 🆕 UNIFIED REAL-TIME SYSTEM (Primary exports)
// export {
//   usePayments,
//   type Payment
// } from '../realtime/hooks/usePayments' // REMOVED - replaced by usePaymentsAPI

export {
  useRealTime,
  useFirestoreListener
} from '../realtime'

// 🔄 LEGACY HOOKS (Deprecated - use unified system above)
// Base Firestore real-time hook - REMOVED
// export {
//   useFirestoreRealTime,
//   type FirestoreRealTimeOptions,
//   type FirestoreRealTimeResult
// } from './useFirestoreRealTime'

// Chat real-time hooks - NEW Firebase Realtime Database implementation
export {
  useRealtimeChat,
  type RealtimeChatMessage,
  type UseRealtimeChatOptions,
  type UseRealtimeChatResult
} from './useRealtimeChat'

// Payment hooks - NEW non-real-time API implementation
export {
  usePaymentsAPI
} from './usePaymentsAPI'
export type { UsePaymentsAPIResult } from './usePaymentsAPI'

// User profile hooks
export {
  useUserProfile
} from './useUserProfile'
export type { UseUserProfileResult } from '../types/user'