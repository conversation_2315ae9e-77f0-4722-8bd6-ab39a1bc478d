/**
 * User Profile Hook
 * Manages user profile data, updates, and avatar uploads
 */

import { useState, useEffect, useCallback } from 'react';
import { useAuth } from '../components/auth/AuthContext';
import { uploadAvatarImage, deleteAvatarImage } from '../utils/imageUpload';
import type {
  UserProfile,
  UserProfileUpdateData,
  UseUserProfileResult,
  UserProfileState
} from '../types/user';

export function useUserProfile(): UseUserProfileResult {
  const { user, getIdToken } = useAuth();
  
  // State
  const [state, setState] = useState<UserProfileState>({
    profile: null as UserProfile | null,
    loading: true,
    saving: false,
    uploading: false,
    error: null,
    lastUpdated: null
  });

  // Fetch user profile from API
  const fetchProfile = useCallback(async () => {
    if (!user) {
      setState(prev => ({ ...prev, loading: false, profile: null }));
      return;
    }

    try {
      setState(prev => ({ ...prev, loading: true, error: null }));
      
      const token = await getIdToken();
      if (!token) {
        throw new Error('No authentication token available');
      }

      const response = await fetch('/auth/user', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      
      setState(prev => ({
        ...prev,
        profile: data.profile,
        loading: false,
        lastUpdated: new Date()
      }));

    } catch (error) {
      console.error('Error fetching user profile:', error);
      setState(prev => ({
        ...prev,
        loading: false,
        error: error instanceof Error ? error.message : 'Failed to fetch profile'
      }));
    }
  }, [user, getIdToken]);

  // Update user profile
  const updateProfile = useCallback(async (updateData: UserProfileUpdateData): Promise<boolean> => {
    if (!user) {
      setState(prev => ({ ...prev, error: 'User not authenticated' }));
      return false;
    }

    try {
      setState(prev => ({ ...prev, saving: true, error: null }));
      
      const token = await getIdToken();
      if (!token) {
        throw new Error('No authentication token available');
      }

      const response = await fetch('/auth/user/profile', {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(updateData)
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      
      if (data.success) {
        setState(prev => ({
          ...prev,
          profile: data.profile,
          saving: false,
          lastUpdated: new Date()
        }));
        return true;
      } else {
        throw new Error(data.message || 'Failed to update profile');
      }

    } catch (error) {
      console.error('Error updating user profile:', error);
      setState(prev => ({
        ...prev,
        saving: false,
        error: error instanceof Error ? error.message : 'Failed to update profile'
      }));
      return false;
    }
  }, [user, getIdToken]);

  // Upload avatar image
  const uploadAvatar = useCallback(async (file: File): Promise<string | null> => {
    if (!user) {
      setState(prev => ({ ...prev, error: 'User not authenticated' }));
      return null;
    }

    try {
      setState(prev => ({ ...prev, uploading: true, error: null }));
      
      // Upload image to Firebase Storage
      const uploadResult = await uploadAvatarImage(file, user.uid);
      
      if (!uploadResult.success || !uploadResult.downloadURL) {
        throw new Error(uploadResult.error || 'Failed to upload image');
      }

      // Update profile with new avatar URL
      const updateSuccess = await updateProfile({ photo_url: uploadResult.downloadURL });
      
      if (updateSuccess) {
        setState(prev => ({ ...prev, uploading: false }));
        return uploadResult.downloadURL;
      } else {
        // If profile update failed, try to clean up uploaded image
        try {
          await deleteAvatarImage(uploadResult.downloadURL);
        } catch (cleanupError) {
          console.warn('Failed to cleanup uploaded image:', cleanupError);
        }
        throw new Error('Failed to update profile with new avatar');
      }

    } catch (error) {
      console.error('Error uploading avatar:', error);
      setState(prev => ({
        ...prev,
        uploading: false,
        error: error instanceof Error ? error.message : 'Failed to upload avatar'
      }));
      return null;
    }
  }, [user, updateProfile]);

  // Refresh profile data
  const refresh = useCallback(async () => {
    await fetchProfile();
  }, [fetchProfile]);

  // Load profile on mount and when user changes
  useEffect(() => {
    fetchProfile();
  }, [fetchProfile]);

  return {
    // Data
    profile: state.profile,
    
    // Loading states
    loading: state.loading,
    saving: state.saving,
    uploading: state.uploading,
    
    // Error state
    error: state.error,
    
    // Actions
    updateProfile,
    uploadAvatar,
    refresh,
    
    // Metadata
    lastUpdated: state.lastUpdated
  };
}
