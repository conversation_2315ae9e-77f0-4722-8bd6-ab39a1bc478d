/**
 * Firebase Configuration for PTT E-commerce Dashboard
 * Modern Firebase v9+ Modular SDK Implementation
 */
import { initializeApp } from 'firebase/app';
import { getAuth, GoogleAuthProvider } from 'firebase/auth';
import {
  getFirestore,
  collection,
  doc,
  onSnapshot,
  query,
  orderBy,
  limit,
  where,
  Timestamp,
  startAfter,
  endBefore,
  getDocs,
  addDoc,
  updateDoc,
  deleteDoc,
  serverTimestamp
} from 'firebase/firestore';
import {
  getDatabase,
  ref,
  push,
  set,
  onValue,
  off,
  serverTimestamp as realtimeServerTimestamp,
  remove
} from 'firebase/database';
import {
  getStorage,
  ref as storageRef,
  uploadBytes,
  getDownloadURL,
  deleteObject
} from 'firebase/storage';
import type {
  DocumentData,
  CollectionReference,
  DocumentReference
} from 'firebase/firestore';

// Firebase configuration
const firebaseConfig = {
  apiKey: "AIzaSyDYIzBLct3ia4M6bDPog_l5Sr7h17vpdcI",
  authDomain: "salebindersync.firebaseapp.com",
  databaseURL: "https://salebindersync-default-rtdb.asia-southeast1.firebasedatabase.app/",
  projectId: "salebindersync",
  storageBucket: "salebindersync.firebasestorage.app",
  messagingSenderId: "315182448302",
  appId: "1:315182448302:web:ec8c9e3a9b254567ee373f",
  measurementId: "G-D327J14MW9"
};

// Initialize Firebase app
export const app = initializeApp(firebaseConfig);

// Initialize Firebase Auth
export const auth = getAuth(app);

// Initialize Firestore
export const db = getFirestore(app);

// Initialize Firebase Realtime Database
export const realtimeDb = getDatabase(app);

// Initialize Firebase Storage
export const storage = getStorage(app);

// Configure Google Auth Provider
export const googleProvider = new GoogleAuthProvider();
googleProvider.addScope('email');
googleProvider.addScope('profile');

// Configure provider parameters
googleProvider.setCustomParameters({
  prompt: 'select_account'
});

// Modern Firestore utilities - v9+ modular exports
export {
  collection,
  doc,
  onSnapshot,
  query,
  orderBy,
  limit,
  where,
  Timestamp,
  startAfter,
  endBefore,
  getDocs,
  addDoc,
  updateDoc,
  deleteDoc,
  serverTimestamp
};

// Firebase Storage utilities - v9+ modular exports
export {
  storageRef,
  uploadBytes,
  getDownloadURL,
  deleteObject
};

// Firebase Realtime Database utilities
export {
  ref,
  push,
  set,
  onValue,
  off,
  realtimeServerTimestamp,
  remove
};

// Type exports (required for isolatedModules)
export type {
  QueryConstraint,
  DocumentData,
  CollectionReference,
  DocumentReference,
  Query,
  QuerySnapshot
} from 'firebase/firestore';

// Collection references with proper typing
export const getPaymentsCollection = (): CollectionReference<DocumentData> =>
  collection(db, 'payments');

export const getChatMessagesCollection = (): CollectionReference<DocumentData> =>
  collection(db, 'chat_messages');

export const getUsersCollection = (): CollectionReference<DocumentData> =>
  collection(db, 'users');

export const getCountersCollection = (): CollectionReference<DocumentData> =>
  collection(db, 'counters');

// Document reference helpers
export const getPaymentDoc = (id: string): DocumentReference<DocumentData> =>
  doc(db, 'payments', id);

export const getUserDoc = (uid: string): DocumentReference<DocumentData> =>
  doc(db, 'users', uid);

export const getCounterDoc = (counterId: string): DocumentReference<DocumentData> =>
  doc(db, 'counters', counterId);

// Firebase Realtime Database reference helpers with PTT prefix
export const getChatMessagesRef = () => ref(realtimeDb, 'ptt_chat/messages');
export const getChatMessageRef = (messageId: string) => ref(realtimeDb, `ptt_chat/messages/${messageId}`);
export const getTypingRef = () => ref(realtimeDb, 'ptt_chat/typing');
export const getUserTypingRef = (userId: string) => ref(realtimeDb, `ptt_chat/typing/${userId}`);

export default app;