/**
 * Image Upload Utilities
 * Handles image compression, upload to Firebase Storage, and URL management
 */

import { storage, storageRef, uploadBytes, getDownloadURL, deleteObject } from '../firebase.config';
import { validateImageFile } from './validation';

export interface ImageUploadResult {
  success: boolean;
  downloadURL?: string;
  error?: string;
}

export interface ImageUploadOptions {
  maxWidth?: number;
  maxHeight?: number;
  quality?: number;
  format?: 'jpeg' | 'webp';
}

/**
 * Compress an image file before upload
 */
export const compressImage = (
  file: File, 
  options: ImageUploadOptions = {}
): Promise<Blob> => {
  const {
    maxWidth = 800,
    maxHeight = 800,
    quality = 0.8,
    format = 'jpeg'
  } = options;

  return new Promise((resolve, reject) => {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    const img = new Image();

    img.onload = () => {
      // Calculate new dimensions while maintaining aspect ratio
      let { width, height } = img;
      
      if (width > height) {
        if (width > maxWidth) {
          height = (height * maxWidth) / width;
          width = maxWidth;
        }
      } else {
        if (height > maxHeight) {
          width = (width * maxHeight) / height;
          height = maxHeight;
        }
      }

      canvas.width = width;
      canvas.height = height;

      // Draw and compress
      ctx?.drawImage(img, 0, 0, width, height);
      
      canvas.toBlob(
        (blob) => {
          if (blob) {
            resolve(blob);
          } else {
            reject(new Error('Failed to compress image'));
          }
        },
        `image/${format}`,
        quality
      );
    };

    img.onerror = () => reject(new Error('Failed to load image'));
    img.src = URL.createObjectURL(file);
  });
};

/**
 * Upload avatar image to Firebase Storage
 */
export const uploadAvatarImage = async (
  file: File,
  userId: string,
  options: ImageUploadOptions = {}
): Promise<ImageUploadResult> => {
  try {
    // Validate file
    const validation = validateImageFile(file);
    if (!validation.valid) {
      return {
        success: false,
        error: validation.error
      };
    }

    // Compress image
    const compressedBlob = await compressImage(file, options);
    
    // Generate unique filename
    const timestamp = Date.now();
    const extension = file.type.split('/')[1];
    const filename = `${timestamp}.${extension}`;
    
    // Create storage reference
    const imageRef = storageRef(storage, `avatars/${userId}/${filename}`);
    
    // Upload file
    const snapshot = await uploadBytes(imageRef, compressedBlob);
    
    // Get download URL
    const downloadURL = await getDownloadURL(snapshot.ref);
    
    return {
      success: true,
      downloadURL
    };
    
  } catch (error) {
    console.error('Error uploading avatar:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to upload image'
    };
  }
};

/**
 * Delete avatar image from Firebase Storage
 */
export const deleteAvatarImage = async (imageUrl: string): Promise<boolean> => {
  try {
    // Extract path from URL
    const url = new URL(imageUrl);
    const pathMatch = url.pathname.match(/\/o\/(.+)\?/);
    
    if (!pathMatch) {
      console.warn('Could not extract path from image URL');
      return false;
    }
    
    const imagePath = decodeURIComponent(pathMatch[1]);
    const imageRef = storageRef(storage, imagePath);
    
    await deleteObject(imageRef);
    return true;
    
  } catch (error) {
    console.error('Error deleting avatar:', error);
    return false;
  }
};

/**
 * Create a preview URL for a selected file
 */
export const createImagePreview = (file: File): string => {
  return URL.createObjectURL(file);
};

/**
 * Cleanup preview URL to prevent memory leaks
 */
export const cleanupImagePreview = (previewUrl: string): void => {
  URL.revokeObjectURL(previewUrl);
};

/**
 * Get file size in human readable format
 */
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};
