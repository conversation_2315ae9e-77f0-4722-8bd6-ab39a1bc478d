/**
 * Image Upload Utilities
 * Handles image compression, upload to Firebase Storage, and URL management
 */

import { storage, storageRef, uploadBytes, getDownloadURL, deleteObject } from '../firebase.config';
import { validateImageFile } from './validation';

export interface ImageUploadResult {
  success: boolean;
  downloadURL?: string;
  error?: string;
}

export interface ImageUploadOptions {
  maxWidth?: number;
  maxHeight?: number;
  quality?: number;
  format?: 'jpeg' | 'webp';
}

/**
 * Compress an image file before upload
 */
export const compressImage = (
  file: File, 
  options: ImageUploadOptions = {}
): Promise<Blob> => {
  const {
    maxWidth = 800,
    maxHeight = 800,
    quality = 0.8,
    format = 'jpeg'
  } = options;

  return new Promise((resolve, reject) => {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    const img = new Image();

    img.onload = () => {
      // Calculate new dimensions while maintaining aspect ratio
      let { width, height } = img;
      
      if (width > height) {
        if (width > maxWidth) {
          height = (height * maxWidth) / width;
          width = maxWidth;
        }
      } else {
        if (height > maxHeight) {
          width = (width * maxHeight) / height;
          height = maxHeight;
        }
      }

      canvas.width = width;
      canvas.height = height;

      // Draw and compress
      ctx?.drawImage(img, 0, 0, width, height);
      
      canvas.toBlob(
        (blob) => {
          if (blob) {
            resolve(blob);
          } else {
            reject(new Error('Failed to compress image'));
          }
        },
        `image/${format}`,
        quality
      );
    };

    img.onerror = () => reject(new Error('Failed to load image'));
    img.src = URL.createObjectURL(file);
  });
};

/**
 * Upload avatar image to Firebase Storage
 */
export const uploadAvatarImage = async (
  file: File,
  userId: string,
  options: ImageUploadOptions = {}
): Promise<ImageUploadResult> => {
  try {
    console.log('Starting avatar upload for user:', userId, 'file:', file.name);

    // Validate file
    const validation = validateImageFile(file);
    if (!validation.valid) {
      console.warn('File validation failed:', validation.error);
      return {
        success: false,
        error: validation.error
      };
    }

    // Compress image
    console.log('Compressing image...');
    const compressedBlob = await compressImage(file, options);
    console.log('Image compressed, size:', compressedBlob.size);

    // Generate unique filename
    const timestamp = Date.now();
    const extension = file.type.split('/')[1];
    const filename = `${timestamp}.${extension}`;

    // Create storage reference
    const imageRef = storageRef(storage, `avatars/${userId}/${filename}`);
    console.log('Storage reference created:', `avatars/${userId}/${filename}`);

    // Upload file
    console.log('Starting upload to Firebase Storage...');
    const snapshot = await uploadBytes(imageRef, compressedBlob);
    console.log('Upload completed, getting download URL...');

    // Get download URL
    const downloadURL = await getDownloadURL(snapshot.ref);
    console.log('Download URL obtained:', downloadURL);

    return {
      success: true,
      downloadURL
    };

  } catch (error) {
    console.error('Error uploading avatar:', error);

    // Provide more specific error messages based on error type
    let errorMessage = 'Failed to upload image';
    if (error instanceof Error) {
      if (error.message.includes('storage/unauthorized')) {
        errorMessage = 'Upload failed: Insufficient permissions. Please check Firebase Storage rules.';
      } else if (error.message.includes('storage/canceled')) {
        errorMessage = 'Upload was canceled';
      } else if (error.message.includes('storage/unknown')) {
        errorMessage = 'Upload failed due to server error. Please try again.';
      } else if (error.message.includes('CORS')) {
        errorMessage = 'Upload failed: CORS configuration issue. Please contact support.';
      } else {
        errorMessage = error.message;
      }
    }

    return {
      success: false,
      error: errorMessage
    };
  }
};

/**
 * Delete avatar image from Firebase Storage
 */
export const deleteAvatarImage = async (imageUrl: string): Promise<boolean> => {
  try {
    // Extract path from URL
    const url = new URL(imageUrl);
    const pathMatch = url.pathname.match(/\/o\/(.+)\?/);
    
    if (!pathMatch) {
      console.warn('Could not extract path from image URL');
      return false;
    }
    
    const imagePath = decodeURIComponent(pathMatch[1]);
    const imageRef = storageRef(storage, imagePath);
    
    await deleteObject(imageRef);
    return true;
    
  } catch (error) {
    console.error('Error deleting avatar:', error);
    return false;
  }
};

/**
 * Create a preview URL for a selected file
 */
export const createImagePreview = (file: File): string => {
  return URL.createObjectURL(file);
};

/**
 * Cleanup preview URL to prevent memory leaks
 */
export const cleanupImagePreview = (previewUrl: string): void => {
  URL.revokeObjectURL(previewUrl);
};

/**
 * Get file size in human readable format
 */
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};
