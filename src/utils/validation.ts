// Validation Utilities
export const validateEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

export const validateProductCode = (code: string): boolean => {
  // PTT product codes are usually alphanumeric, 3-15 characters
  const codeRegex = /^[A-Z0-9]{3,15}$/
  return codeRegex.test(code.toUpperCase())
}

export const validateAmount = (amount: string | number): boolean => {
  const numericAmount = typeof amount === 'string' ? parseFloat(amount) : amount
  return !isNaN(numericAmount) && numericAmount >= 0
}

export const sanitizeProductCodes = (input: string): string[] => {
  return input
    .split('\n')
    .map(code => code.trim().toUpperCase())
    .filter(code => code.length > 0 && validateProductCode(code))
}

export const validateNickname = (nickname: string): boolean => {
  // Nickname validation: 2-50 characters, alphanumeric + spaces, hyphens, underscores
  if (!nickname || nickname.length < 2 || nickname.length > 50) {
    return false
  }

  // Allow letters, numbers, spaces, hyphens, and underscores
  const nicknameRegex = /^[a-zA-Z0-9\s\-_]+$/
  return nicknameRegex.test(nickname.trim())
}

export const validateImageFile = (file: File): { valid: boolean; error?: string } => {
  // Check file type
  const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp']
  if (!allowedTypes.includes(file.type)) {
    return {
      valid: false,
      error: 'Please select a valid image file (JPG, PNG, or WebP)'
    }
  }

  // Check file size (max 5MB)
  const maxSize = 5 * 1024 * 1024 // 5MB in bytes
  if (file.size > maxSize) {
    return {
      valid: false,
      error: 'Image size must be less than 5MB'
    }
  }

  return { valid: true }
}