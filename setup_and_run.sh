#!/bin/bash

# PTT E-commerce Dashboard Setup and Run Script
# This script handles all setup requirements and starts the application
#
# ARCHITECTURE: React + Vite Frontend + Flask Backend
# - React frontend built with Vite and TypeScript
# - Components compiled into UMD library (ptt-components.js) for Flask integration
# - Flask backend provides APIs and serves the integrated application
# - Application runs on http://127.0.0.1:3000
#
# IMPORTANT: This script automatically detects and uses a compatible Python version.
# Python 3.13 is avoided due to pydantic-core compilation issues.
# Preferred versions: Python 3.10 or 3.11

set -e  # Exit on error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[✓]${NC} $1"
}

print_error() {
    echo -e "${RED}[✗]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[!]${NC} $1"
}

# Get the script directory
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
cd "$SCRIPT_DIR"

echo "======================================"
echo "PTT E-commerce Dashboard Setup Script"
echo "======================================"
echo ""

# Step 1: Kill any existing processes on port 3000
print_status "Checking for processes on port 3000..."
if lsof -ti:3000 > /dev/null 2>&1; then
    print_warning "Found process on port 3000, killing it..."
    lsof -ti:3000 | xargs kill -9 2>/dev/null || true
    sleep 1
fi

# Step 2: Check Python version and find compatible version
print_status "Checking Python version..."

# Function to check if a Python version is compatible
check_python_compatibility() {
    local python_cmd=$1
    if ! command -v "$python_cmd" &> /dev/null; then
        return 1
    fi

    local version=$($python_cmd -c 'import sys; print(".".join(map(str, sys.version_info[:2])))')
    local major=$(echo $version | cut -d. -f1)
    local minor=$(echo $version | cut -d. -f2)

    # Check if version is compatible (3.8-3.12, avoid 3.13 due to pydantic-core issues)
    if [ "$major" -eq 3 ] && [ "$minor" -ge 8 ] && [ "$minor" -le 12 ]; then
        echo "$python_cmd"
        return 0
    fi
    return 1
}

# Try to find a compatible Python version (prefer 3.10, 3.11, then others)
# Avoid Python 3.13 due to pydantic-core compilation issues
PYTHON_CMD=""
for py_version in python3.10 python3.11 python3.9 python3.8 python3.12 python3; do
    if PYTHON_CMD=$(check_python_compatibility "$py_version"); then
        break
    fi
done

if [ -z "$PYTHON_CMD" ]; then
    print_error "No compatible Python version found!"
    print_error "Please install Python 3.8-3.12 (avoid Python 3.13 due to pydantic-core compilation issues)"
    print_error "Recommended: Python 3.10 or 3.11 for best compatibility"
    print_error ""
    print_error "Installation suggestions:"
    print_error "  macOS: brew install python@3.10"
    print_error "  Ubuntu: sudo apt install python3.10 python3.10-venv"
    print_error "  Windows: Download from python.org (choose 3.10.x)"
    exit 1
fi

PYTHON_VERSION=$($PYTHON_CMD -c 'import sys; print(".".join(map(str, sys.version_info[:2])))')
print_status "Using Python: $PYTHON_CMD (version $PYTHON_VERSION)"

# Warn if Python 3.13 is detected but not being used
if command -v python3 &> /dev/null; then
    SYSTEM_PYTHON_VERSION=$(python3 -c 'import sys; print(".".join(map(str, sys.version_info[:2])))')
    if [ "$SYSTEM_PYTHON_VERSION" = "3.13" ] && [ "$PYTHON_CMD" != "python3" ]; then
        print_warning "Python 3.13 detected but not used due to pydantic-core compatibility issues"
        print_warning "Using $PYTHON_CMD instead for better compatibility"
    fi
fi

# Step 3: Check Node.js and npm
print_status "Checking Node.js and npm..."
if ! command -v node &> /dev/null; then
    print_error "Node.js is not installed. Please install Node.js 16 or higher."
    exit 1
fi

if ! command -v npm &> /dev/null; then
    print_error "npm is not installed. Please install npm."
    exit 1
fi

NODE_VERSION=$(node -v)
NPM_VERSION=$(npm -v)
print_status "Node.js version: $NODE_VERSION"
print_status "npm version: $NPM_VERSION"

# Step 4: Frontend setup (React + Vite + TypeScript)
print_status "Setting up React frontend with Vite and TypeScript..."

# Clean npm cache and node_modules for fresh install
print_status "Cleaning npm cache and node_modules..."

# Clean npm cache (skip timeout on macOS)
if command -v timeout >/dev/null 2>&1; then
    if ! timeout 30 npm cache clean --force 2>/dev/null; then
        print_warning "npm cache clean timed out, continuing anyway..."
    fi
else
    # macOS doesn't have timeout by default
    npm cache clean --force 2>/dev/null || {
        print_warning "npm cache clean failed, continuing anyway..."
    }
fi

# Remove node_modules and package-lock.json efficiently
if [ -d "node_modules" ]; then
    print_status "Removing existing node_modules directory..."

    # Count files to estimate removal time
    FILE_COUNT=$(find node_modules -type f 2>/dev/null | wc -l | tr -d ' ')
    if [ "$FILE_COUNT" -gt 5000 ]; then
        print_warning "Large node_modules directory detected ($FILE_COUNT files)"
        print_warning "This may take 1-2 minutes. If it hangs, press Ctrl+C and run:"
        print_warning "  rm -rf node_modules && ./setup_and_run.sh"
    fi

    # Try removal (with timeout if available)
    if command -v timeout >/dev/null 2>&1; then
        if ! timeout 120 rm -rf node_modules 2>/dev/null; then
            print_error "Failed to remove node_modules directory (timed out after 2 minutes)"
            print_error ""
            print_error "SOLUTION: Please run these commands manually:"
            print_error "  rm -rf node_modules package-lock.json"
            print_error "  ./setup_and_run.sh"
            exit 1
        fi
    else
        # macOS - no timeout available, just try removal
        if ! rm -rf node_modules 2>/dev/null; then
            print_error "Failed to remove node_modules directory"
            print_error ""
            print_error "SOLUTION: Please run these commands manually:"
            print_error "  rm -rf node_modules package-lock.json"
            print_error "  ./setup_and_run.sh"
            exit 1
        fi
    fi

    print_status "node_modules directory removed successfully"
fi

if [ -f "package-lock.json" ]; then
    rm -f package-lock.json 2>/dev/null || {
        print_warning "Could not remove package-lock.json"
    }
fi

print_status "Cleanup completed"

# Install npm dependencies with proper timeout and error handling
print_status "Installing frontend dependencies (React, Vite, TypeScript)..."
print_warning "This may take 2-3 minutes depending on your internet connection..."

# Check if package.json exists
if [ ! -f "package.json" ]; then
    print_error "package.json not found in current directory"
    print_error "Please ensure you're running this script from the project root"
    exit 1
fi

# Install dependencies with timeout (if available) and detailed error reporting
if command -v timeout >/dev/null 2>&1; then
    # Linux/systems with timeout
    if timeout 180 npm install --no-optional --no-audit --no-fund; then
        print_status "Frontend dependencies installed successfully"
    else
        INSTALL_EXIT_CODE=$?
        if [ $INSTALL_EXIT_CODE -eq 124 ]; then
            print_error "npm install timed out after 3 minutes"
            print_error "This usually indicates network issues or very slow connection"
        else
            print_error "npm install failed with exit code: $INSTALL_EXIT_CODE"
        fi
        print_error ""
        print_error "To debug, try running manually: npm install --verbose"
        exit 1
    fi
else
    # macOS/systems without timeout - just run npm install
    print_warning "Running npm install without timeout (this may take several minutes)..."
    if npm install --no-optional --no-audit --no-fund; then
        print_status "Frontend dependencies installed successfully"
    else
        INSTALL_EXIT_CODE=$?
        print_error "npm install failed with exit code: $INSTALL_EXIT_CODE"
        print_error "Common causes:"
        print_error "  - Network connectivity issues"
        print_error "  - Incompatible Node.js version"
        print_error "  - Disk space issues"
        print_error "  - Permission problems"
        print_error ""
        print_error "To debug, try running manually: npm install --verbose"
        exit 1
    fi
fi

# Verify TypeScript compiler is available
if [ ! -f "node_modules/.bin/tsc" ]; then
    print_error "TypeScript compiler not found in node_modules/.bin/"
    print_error "npm install may have failed silently"
    print_error "Try running: npm list typescript"
    exit 1
fi

print_status "TypeScript compiler available"

# Run TypeScript type checking (with timeout if available)
print_status "Running TypeScript type checking..."
if command -v timeout >/dev/null 2>&1; then
    if timeout 60 npx tsc --noEmit; then
        print_status "TypeScript type checking passed"
    else
        TYPE_CHECK_EXIT_CODE=$?
        if [ $TYPE_CHECK_EXIT_CODE -eq 124 ]; then
            print_warning "TypeScript type checking timed out - continuing with build anyway"
        else
            print_warning "TypeScript type checking failed - continuing with build anyway"
        fi
    fi
else
    # No timeout available
    if npx tsc --noEmit; then
        print_status "TypeScript type checking passed"
    else
        print_warning "TypeScript type checking failed - continuing with build anyway"
    fi
fi

# Build React component library for Flask backend
print_status "Building React component library with Vite..."
if command -v timeout >/dev/null 2>&1; then
    if timeout 120 npm run build; then
        print_status "Component library build completed successfully"
    else
        BUILD_EXIT_CODE=$?
        if [ $BUILD_EXIT_CODE -eq 124 ]; then
            print_error "Build timed out after 2 minutes"
        else
            print_error "Build failed with exit code: $BUILD_EXIT_CODE"
        fi
        print_error "This will prevent the web interface from working"
        exit 1
    fi
else
    # No timeout available
    if npm run build; then
        print_status "Component library build completed successfully"
    else
        print_error "Build failed"
        print_error "This will prevent the web interface from working"
        exit 1
    fi
fi

# Verify component library was generated
if [ -f "backend/app/static/scripts/ptt-components.js" ]; then
    COMPONENT_SIZE=$(du -h "backend/app/static/scripts/ptt-components.js" | cut -f1)
    print_status "Component library generated: ptt-components.js ($COMPONENT_SIZE)"
else
    print_error "Component library file not found!"
    print_error "Expected: backend/app/static/scripts/ptt-components.js"
    print_error "Build may have failed or output directory is incorrect"
    exit 1
fi

# Step 5: Check for Firebase key
print_status "Checking Firebase configuration..."
FIREBASE_KEY_IN_ROOT="$SCRIPT_DIR/CheckMisa_Firebase_Key.json"
FIREBASE_KEY_IN_SECRETS="$SCRIPT_DIR/backend/config/secrets/firebase_key.json"
FIREBASE_KEY_EXPECTED="$SCRIPT_DIR/CheckMisa_Firebase_Key.json"

if [ ! -f "$FIREBASE_KEY_IN_ROOT" ]; then
    if [ -f "$FIREBASE_KEY_IN_SECRETS" ]; then
        print_warning "Copying Firebase key from secrets directory..."
        cp "$FIREBASE_KEY_IN_SECRETS" "$FIREBASE_KEY_EXPECTED"
    else
        print_error "Firebase key not found! Please ensure one of these files exists:"
        print_error "  - $FIREBASE_KEY_IN_ROOT"
        print_error "  - $FIREBASE_KEY_IN_SECRETS"
        exit 1
    fi
fi

# Step 6: Clean and setup backend dependencies
print_status "Setting up Flask backend environment..."

# Change to backend directory
if [ ! -d "backend" ]; then
    print_error "Backend directory not found!"
    print_error "Please ensure you're running this script from the project root"
    exit 1
fi

cd backend

# Clean Python cache
print_status "Cleaning Python cache..."
find . -type d -name "__pycache__" -exec rm -rf {} + 2>/dev/null || true
find . -name "*.pyc" -delete 2>/dev/null || true

# Check if requirements.txt exists
if [ ! -f "requirements.txt" ]; then
    print_error "requirements.txt not found in backend directory"
    print_error "Cannot install Python dependencies"
    exit 1
fi

# Check if virtual environment exists and is compatible
VENV_PYTHON=""
if [ -d "venv" ] && [ -f "venv/bin/python" ]; then
    VENV_PYTHON_VERSION=$(venv/bin/python -c 'import sys; print(".".join(map(str, sys.version_info[:2])))' 2>/dev/null || echo "unknown")
    if [ "$VENV_PYTHON_VERSION" = "$PYTHON_VERSION" ]; then
        print_status "Compatible virtual environment found (Python $VENV_PYTHON_VERSION)"
        VENV_PYTHON="venv/bin/python"
    else
        print_warning "Virtual environment Python version mismatch ($VENV_PYTHON_VERSION vs $PYTHON_VERSION)"
        print_status "Removing incompatible virtual environment..."
        rm -rf venv
    fi
fi

# Create virtual environment if needed
if [ ! -d "venv" ]; then
    print_status "Creating fresh Python virtual environment with $PYTHON_CMD..."
    if ! $PYTHON_CMD -m venv venv; then
        print_error "Failed to create virtual environment"
        print_error "Please ensure $PYTHON_CMD is properly installed"
        exit 1
    fi
    VENV_PYTHON="venv/bin/python"
fi

# Activate virtual environment
print_status "Activating virtual environment..."
if ! source venv/bin/activate; then
    print_error "Failed to activate virtual environment"
    exit 1
fi

# Upgrade pip to latest version
print_status "Upgrading pip to latest version..."
if ! pip install --upgrade pip --quiet; then
    print_warning "Failed to upgrade pip, continuing with current version"
fi

# Install all production dependencies from requirements.txt
print_status "Installing Python dependencies..."
if pip install -r requirements.txt --quiet; then
    print_status "Python dependencies installed successfully"
else
    print_error "Failed to install Python dependencies"
    print_error "Common causes:"
    print_error "  - Missing system dependencies"
    print_error "  - Incompatible Python version"
    print_error "  - Network connectivity issues"
    print_error ""
    print_error "To debug, try running manually:"
    print_error "  cd backend"
    print_error "  source venv/bin/activate"
    print_error "  pip install -r requirements.txt --verbose"
    exit 1
fi

# Step 7: Create necessary directories
print_status "Creating necessary directories..."
mkdir -p uploads
mkdir -p app/static/scripts
mkdir -p app/static/css
mkdir -p app/static/images

# Step 8: Verify setup and start the Flask server
print_status "Verifying setup completion..."

# Check if component library was built
if [ -f "app/static/scripts/ptt-components.js" ]; then
    COMPONENT_SIZE=$(du -h "app/static/scripts/ptt-components.js" | cut -f1)
    print_status "✅ React component library ready: ptt-components.js ($COMPONENT_SIZE)"
else
    print_error "❌ React component library not found!"
    print_error "Expected: backend/app/static/scripts/ptt-components.js"
    exit 1
fi

# Check if Python dependencies are working
if python -c "import flask, firebase_admin" 2>/dev/null; then
    print_status "✅ Python dependencies verified"
else
    print_error "❌ Python dependencies verification failed"
    exit 1
fi

print_status "Starting Flask development server with React integration..."
echo ""
echo "============================================================"
echo "🚀 PTT E-commerce Dashboard"
echo "============================================================"
echo "Architecture: React + Vite Frontend + Flask Backend"
echo "Server URL: http://127.0.0.1:3000"
echo "Component Library: backend/app/static/scripts/ptt-components.js"
echo "Frontend Source: src/ (TypeScript + React)"
echo "Backend Source: backend/ (Python + Flask)"
echo ""
echo "Features Available:"
echo "  • Payment Management with SMS Classification"
echo "  • Enhanced Bank Detection (Vietinbank, Vietcombank, Techcombank)"
echo "  • Real-time Firebase Integration"
echo "  • Misa Check Tool"
echo ""
echo "Press Ctrl+C to stop the server"
echo "============================================================"
echo ""

# Set environment variables
export FLASK_APP=app
export FLASK_ENV=development
export FLASK_DEBUG=1

# Run the server
python run.py