{"content": {"_creators": null, "architecture": 64, "base_exec_prefix": "/nix/store/xf54733x4chbawkh1qvy9i1i4mlscy1c-python3-3.10.11", "base_prefix": "/nix/store/xf54733x4chbawkh1qvy9i1i4mlscy1c-python3-3.10.11", "distutils_install": {"data": "", "headers": "include/python3.10/UNKNOWN", "platlib": "lib/python3.10/site-packages", "purelib": "lib/python3.10/site-packages", "scripts": "bin"}, "exec_prefix": "/nix/store/xf54733x4chbawkh1qvy9i1i4mlscy1c-python3-3.10.11", "executable": "/nix/store/xf54733x4chbawkh1qvy9i1i4mlscy1c-python3-3.10.11/bin/python3.10", "file_system_encoding": "utf-8", "has_venv": true, "implementation": "CPython", "max_size": 9223372036854775807, "original_executable": "/nix/store/xf54733x4chbawkh1qvy9i1i4mlscy1c-python3-3.10.11/bin/python3.10", "os": "posix", "path": ["/nix/store/kglgy8nffq9gpdnh517h73w3x8zcqhsn-poetry-in-venv/env/lib/python3.10/site-packages/virtualenv/discovery", "/nix/store/icx0zbk2r2qrpnqpd41q4h4xzr856d4f-python3.10-setuptools-67.4.0/lib/python3.10/site-packages", "/nix/store/xf54733x4chbawkh1qvy9i1i4mlscy1c-python3-3.10.11/lib/python3.10", "/home/<USER>/Ecom-Dashboard/.pythonlibs/lib/python3.10/site-packages", "/nix/store/s4cbcvnm0miclkjwj6g8fxcn8fgb78s1-python3.10-pip-21.2.dev0/lib/python3.10/site-packages", "/nix/store/xf54733x4chbawkh1qvy9i1i4mlscy1c-python3-3.10.11/lib/python310.zip", "/nix/store/xf54733x4chbawkh1qvy9i1i4mlscy1c-python3-3.10.11/lib/python3.10/lib-dynload", "/nix/store/xf54733x4chbawkh1qvy9i1i4mlscy1c-python3-3.10.11/lib/python3.10/site-packages"], "platform": "linux", "prefix": "/nix/store/xf54733x4chbawkh1qvy9i1i4mlscy1c-python3-3.10.11", "real_prefix": null, "stdout_encoding": "utf-8", "sysconfig": {"makefile_filename": "/nix/store/xf54733x4chbawkh1qvy9i1i4mlscy1c-python3-3.10.11/lib/python3.10/config-3.10-x86_64-linux-gnu/Makefile"}, "sysconfig_paths": {"data": "{base}", "include": "{installed_base}/include/python{py_version_short}{abiflags}", "platlib": "{platbase}/{platlibdir}/python{py_version_short}/site-packages", "platstdlib": "{platbase}/{platlibdir}/python{py_version_short}", "purelib": "{base}/lib/python{py_version_short}/site-packages", "scripts": "{base}/bin", "stdlib": "{installed_base}/{platlibdir}/python{py_version_short}"}, "sysconfig_scheme": null, "sysconfig_vars": {"PYTHONFRAMEWORK": "", "abiflags": "", "base": "/nix/store/xf54733x4chbawkh1qvy9i1i4mlscy1c-python3-3.10.11", "installed_base": "/nix/store/xf54733x4chbawkh1qvy9i1i4mlscy1c-python3-3.10.11", "platbase": "/nix/store/xf54733x4chbawkh1qvy9i1i4mlscy1c-python3-3.10.11", "platlibdir": "lib", "py_version_short": "3.10"}, "system_executable": "/nix/store/xf54733x4chbawkh1qvy9i1i4mlscy1c-python3-3.10.11/bin/python3.10", "system_stdlib": "/nix/store/xf54733x4chbawkh1qvy9i1i4mlscy1c-python3-3.10.11/lib/python3.10", "system_stdlib_platform": "/nix/store/xf54733x4chbawkh1qvy9i1i4mlscy1c-python3-3.10.11/lib/python3.10", "version": "3.10.11 (main, Apr  4 2023, 22:10:32) [GCC 12.2.0]", "version_info": {"major": 3, "micro": 11, "minor": 10, "releaselevel": "final", "serial": 0}, "version_nodot": "310"}, "path": "/nix/store/xf54733x4chbawkh1qvy9i1i4mlscy1c-python3-3.10.11/bin/python3.10", "st_mtime": 1.0}